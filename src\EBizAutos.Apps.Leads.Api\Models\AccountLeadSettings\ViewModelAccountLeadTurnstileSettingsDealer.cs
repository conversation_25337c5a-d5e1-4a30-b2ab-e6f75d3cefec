using System.Collections.Generic;
using EBizAutos.ApplicationCommonLib.Applications.Enums;
using EBizAutos.Apps.Leads.CommonLib.Models.Turnstile;

namespace EBizAutos.Apps.Leads.Api.Models.AccountLeadSettings {
    public class ViewModelAccountLeadTurnstileSettingsDealer {
        public ViewModelAccountLeadTurnstileSettingsDealer() {
            LeadTypeSettings = new List<ViewModelTurnstileLeadTypeSpecificSetting>();
        }

        public int AccountId { get; set; }
        public bool IsTurnstileEnabled { get; set; }
        public string FailureMessage { get; set; }
        public TurnstileFailureActionEnum FailureAction { get; set; }
        public List<ViewModelTurnstileLeadTypeSpecificSetting> LeadTypeSettings { get; set; }
    }
}
