using EBizAutos.Apps.Leads.CommonLib.Models.Fees;
using EBizAutos.Apps.Leads.CommonLib.Models.Turnstile;
using MongoDB.Bson.Serialization.Attributes;

namespace EBizAutos.Apps.Leads.CommonLib.Models {
	public class AccountLeadSettings {
		public AccountLeadSettings() {
			ManagementSettings = new AccountLeadManagementSettings();
			SummaryReportSettings = new AccountLeadSummaryReportSettings();
			TurnstileSettings = new AccountLeadTurnstileSettings();
		}

		[BsonId]
		public int AccountId { get; set; }
		public bool IsActive { get; set; }
		public LeadFees FeesForTwilio { get; set; }
		public AccountLeadNotificationSettings NotificationSettings { get; set; }
		public bool HasToUsePaidUsers { get; set; }
		public AccountLeadManagementSettings ManagementSettings { get; set; }
		public AccountLeadSummaryReportSettings SummaryReportSettings { get; set; }
		public AccountLeadTurnstileSettings TurnstileSettings { get; set; }
		public string CompanyName { get; set; }
		public int AccountSettingsVersion { get; private set; }

		public void SetAccountSettingsVersion(int version) {
			AccountSettingsVersion = version;
		}
	}
}
