{"AppSettings": {"ServiceName": "AppsLeadsMicroservice", "ServiceDisplayName": "AppsLeadsMicroservice", "ServiceDescription": "Apps Leads Microservice", "ServiceEventLogName": "AppsLeadsLog", "ApplicationName": "Apps Leads Service (dev)", "DataAccessEncryptKey": "fpABRddPOOg0hbm1PUHIjw==", "DataAccessEncryptIV": "AAAAAAAAAAAAAAAAAAAAAA==", "LegacyDataAccessEncryptKey": "7kkRcaz5l2TvAQzGxfKLF4a7oFlsrn6W", "LegacyDataAccessEncryptIV": "7rmR5t39n2sd", "IsLive": false, "HasToSendContactsProblemNotificationsToSlack": false}, "AppsWebAppConfiguration": {"LeadsCommunicationManageUrlTemplate": "http://apps.dev.ebizautos/leads/{0}/campaign/{1}/{2}"}, "AppsApiSettings": {"AppsBaseUrl": "http://apps.dev.ebizautos/", "RequestTimeoutInMs": 30000, "AppsAuthorizationToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VyU2Vzc2lvbklkIjoiZTQyYmQwMmYtM2EzMi00NTE1LWFmNzYtMzVkMWZmNTNmOWVlIiwiVXNlcklkIjoiNWIwM2QxOWQ4N2M5M2IxYzMwN2FhZThmIiwiaXNzIjoiQXBwc0FwaVNlcnZlciIsImF1ZCI6IkFwcHNBcGlBdWRpZW5jZSJ9.nNYUb3rCK3ejJLn2C_qxSg3gwnB79jnQgs3RceR7dIo"}, "DbSettings": {"AppsAccountsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/accounts?retryWrites=true&w=majority", "AppsSitesMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/sites?retryWrites=true&w=majority", "AppsLeadsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsleads?retryWrites=true&w=majority", "AppsLeadsMsSqlRepositoryConnectionString": "server=dev-sql.internal.aws.ebizautos.com; database=AppsLeads; User ID=sa; Pwd=********; Max Pool Size=300; Connection Timeout=30; Application Name=AppsLeads;", "AppsLeadsLogsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsleadslogs?retryWrites=true&w=majority", "EBizAutosMsSqlRepositoryConnectionString": "server=dev-sql.internal.aws.ebizautos.com; database=EBizAutos; User ID=sa; Pwd=********; Max Pool Size=300; Connection Timeout=30; Application Name=AppsLeads;", "AppsLeadsSearchDataMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsleadssearchdata?retryWrites=true&w=majority", "GalleryDataModificationTasksConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsgallerysynchronizationdata?retryWrites=true&w=majority", "VehicleMsSqlRepositoryTryCount": 3, "VehicleMsSqlRepositoryMinRetryDelayInMs": 500, "VehicleMsSqlRepositoryMaxRetryDelayInMs": 1000}, "NotificationSettings": {"DealerSocketApiUrl": "https://oemwebsecure.dealersocket.com/DSOEMLead/US/DCP/adf/1/SalesLead", "DealerSocketUserName": "802LFP6899", "DealerSocketPassword": "644MJZ9392", "EmailsSubstitution": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com"], "EmailValidationPattern": "^[_a-zA-Z0-9-]+(\\.[_a-zA-Z0-9-]+)*@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.(([0-9]{1,3})|([a-zA-Z]{2,3})|(aero|coop|info|museum|name))$", "MailServer": "email-smtp.us-east-1.amazonaws.com", "MailPort": 587, "MailUserName": "AKIAJ63DJM7JPLD4746A", "MailPassword": "AkQS3wwECygPDx6tSkdHWmvtVW5P76sm5QbBak3Y6JjY", "MailEnableSsl": true, "EmailFrom": "<EMAIL>", "ErrorEmailsTo": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com"], "IsLoggingOn": true, "UnitedShippingWebServiceUrl": "https://new.vehicletransportusa.com/api/penske/quote", "UnitedShippingAuthorizationHeader": "BP49sRBwhkng8uVSY3YA5wTa"}, "EmailProxySettings": {"ForwardBoxPop3Server": "sandbox-email-proxy-01.internal.aws.ebizautos.com", "ForwardBoxUserName": "<EMAIL>", "ForwardBoxUserPassword": "eBiz89148#", "ForwardBoxDomain": "ebizautos.tv", "IsSecureSertificateValidationTurnedOff": true, "MailServer": "email-smtp.us-east-1.amazonaws.com", "MailPort": 587, "MailUserName": "AKIAJ63DJM7JPLD4746A", "MailPassword": "AkQS3wwECygPDx6tSkdHWmvtVW5P76sm5QbBak3Y6JjY", "MailEnableSsl": true, "EmailFromForRedirectedEmails": "<EMAIL>", "MaxEmailsToReadCount": 10}, "TwilioSettings": {"AccountSid": "**********************************", "AuthToken": "2c8d79bc082dc1ae81107e556d4745cb", "DealerApplicationSid": "APfdfe4a31dd85b95bb9a89cd1ee07eacf", "CustomerApplicationSid": "AP61272a2da90063e436f1c30dfea8195a", "ApiUrl": "https://api.twilio.com", "MessageStatusCallbackUrl": "http://7083a0d9.ngrok.io/api/webhook/message/callback/", "RecordingStatusCallbackUrl": "http://7083a0d9.ngrok.io/api/webhook/call/recording/callback/", "LookupPricing": {"CallerNamePrice": 0.01}, "DownloadSettings": {"InitialDirectory": "D:\\ExternalData\\twiliocalls\\", "UserName": "ebizcolo\\svadmin", "Password": "pa$$werd99"}, "DeleteSettings": {"MaxStorageTimeInMonths": 6}, "ApiErrorCodesForRetry": [21422], "AudioRecordsHost": "http://externaldata.sandbox.ebizautos.com/twiliocalls", "GetPricingCallTimeoutInMs": 1000, "CountOfTriesToGetPricing": 5, "AmountOfTriesToBuyPhoneNumber": 2, "UserProxyPhoneBuyTimeoutInMs": 60000, "UserProxyPhoneBuyLockRecheckTimeoutInMs": 2000, "MaxAllowedDistanceInMilesForFindingNumber": 150, "MessagingServiceFilter": "eBizAutos Leads Campaigns"}, "IntegrityReportSettings": {"ProcessingStartTime": "12:00", "ProcessingAttemptsCount": 3, "ProcessingDelayOnRetry": "00:01", "IsLoggingOn": true}, "UtilitySettings": {"CloseAccountDelayInDays": 1, "EmptyCommumicationContactsErrorEmailsTo": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com"], "ArchivedConversationDetailsAccountsReadBatchSize": 100, "ArchivedConversationDetailsReadBatchIntervalInMs": 200, "ArchivedConversationDetailsExecutionIntervalInHr": 6, "ArchivedConversationDetailsSelfCheckIntervalInMs": 1000, "ArchivedConversationDetailsFailedExecutionDelayInMs": 5000, "ConversationDetailsArchivingAccountsReadBatchSize": 50, "ConversationDetailsArchivingReadBatchSize": 50, "ConversationDetailsArchivingReadBatchIntervalInMs": 200, "ConversationDetailsArchivingExecutionIntervalInHr": 6, "ConversationDetailsArchivingSelfCheckIntervalInMs": 1000, "ConversationDetailsArchivingFailedExecutionDelayInMs": 5000}, "ServiceBusSettings": {"Host": "b-14921ad0-337a-4458-84ee-c042b1327094-1.mq.us-east-1.amazonaws.com", "Port": 61617, "Username": "ebizeventbus", "Password": "z$32s0d-nd8G62!js83aPuhpUh", "FailoverHosts": ["b-14921ad0-337a-4458-84ee-c042b1327094-1.mq.us-east-1.amazonaws.com"], "HasToUseSsl": true, "IsLoggingOn": true, "LogsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsservicebuslogs?retryWrites=true&w=majority", "ReceiveEndpointSettings": {"EBayReceiveEndpointSettings": {"EBayEventsEndpointEnabled": true, "EBayEventsEndpointConsumersCount": 1, "EBayEventsEndpointRetries": 1, "EBayEventsEndpointRetriesIntervalInSec": 300, "IsEBayAuctionOfferCreatedEventRedeliveryEnabled": true, "EBayAuctionOfferCreatedEventRedeliveryCount": 3, "EBayAuctionOfferCreatedEventRedeliveryTimeoutInMin": 30, "IsEBayNotificationEventRedeliveryEnabled": true, "EBayNotificationEventRedeliveryCount": 3, "EBayNotificationEventRedeliveryTimeoutInMin": 30}, "AccountReceiveEnpointSettings": {"IsAccountEventsEndpointEnabled": true, "AccountEventsEndpointConsumersCount": 1, "AccountEventsEndpointRetries": 1, "AccountEventsEndpointRetriesIntervalInSec": 300, "IsAccountEventsEnpointRedeliveryEnabled": true, "AccountEventsEnpointRedeliveryCount": 2, "AccountEventsEnpointRedeliveryTimeoutInMinutes": 30}, "WebFormLeadReceiveEnpointSettings": {"IsEventsEndpointEnabled": true, "EventsEndpointConsumersCount": 1}}, "PublishSettings": {"RetryAttempts": 3, "MinRetryDelayInMs": 5000, "MaxRetryDelayInMs": 10000}}, "ExceptionSettings": {"ErrorWebServiceLocation": "http://errors.aws.ebizautos.com/queuedservice.asmx", "ApplicationCategoryId": 9, "MailServer": "email-smtp.us-east-1.amazonaws.com", "MailPort": 587, "MailUserName": "AKIAJ63DJM7JPLD4746A", "MailPassword": "AkQS3wwECygPDx6tSkdHWmvtVW5P76sm5QbBak3Y6JjY", "MailEnableSsl": true, "ErrorEmailFrom": "<EMAIL>", "ErrorEmailTo": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com"], "EmailCc": [], "HasToUseServiceBus": false}, "HubsSettings": {"SMSHubSettings": {"HashingHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 5, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 180000, "TasksLoadingActivationBound": 3, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 1200000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 100, "FailedSpinDelayInMs": 2000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 5, "NoTaskDelayMaxInMs": 10, "MaxProcessingAttemptsCount": 10}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:00:55", "TaskSuspendingExpirationTime": "00:10:00", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "VoiceHubSettings": {"HashingHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 5, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 180000, "TasksLoadingActivationBound": 3, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 100, "FailedSpinDelayInMs": 2000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 5, "NoTaskDelayMaxInMs": 10, "MaxProcessingAttemptsCount": 10}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:00:55", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "StatisticHubSettings": {"HashingHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 5, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 180000, "TasksLoadingActivationBound": 0, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 100, "FailedSpinDelayInMs": 2000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 5, "NoTaskDelayMaxInMs": 10, "MaxProcessingAttemptsCount": 3}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:00:55", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "UtilityHubSettings": {"HashingHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 5, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 180000, "TasksLoadingActivationBound": 3, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 100, "FailedSpinDelayInMs": 2000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 5, "NoTaskDelayMaxInMs": 10, "MaxProcessingAttemptsCount": 10}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:00:55", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "EmailHubSettings": {"HashingHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 5, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 180000, "TasksLoadingActivationBound": 3, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 100, "FailedSpinDelayInMs": 2000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 5, "NoTaskDelayMaxInMs": 10, "MaxProcessingAttemptsCount": 3}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:00:55", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "EmailForwardHubSettings": {"TaskCheckFruequencyInMs": 100, "CountOfTasksPerCheck": 10, "CountOfTriesToProcessTask": 3, "TimeIntervalToReprocessFailedTasksInMin": 30, "OnErrorThreadSleepingTimeInSec": 60, "OnSuccessThreadSleepingTimeInSec": 30}, "FailedTasksHubSettings": {"TaskCheckFruequencyInMs": 60000, "CountOfTasksPerCheck": 10, "CountOfTriesToProcessTask": 3, "TimeIntervalToReprocessFailedTasksInMin": 30, "DelayBeforeSendingFirstMessageTimeInSec": 1, "OnErrorThreadSleepingTimeInSec": 60, "ActorAssumeFreezedTimeoutInSec": 900}, "SynchronizationHubSettings": {"HashingHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 5, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 180000, "TasksLoadingActivationBound": 3, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 100, "FailedSpinDelayInMs": 2000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 5, "NoTaskDelayMaxInMs": 10, "MaxProcessingAttemptsCount": 10}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:00:55", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "SynchronizationLowPriorityHubSettings": {"HashingHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 5, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 180000, "TasksLoadingActivationBound": 3, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 100, "FailedSpinDelayInMs": 2000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 5, "NoTaskDelayMaxInMs": 10, "MaxProcessingAttemptsCount": 10}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:00:55", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "WebFormHubSettings": {"WebFormHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 5, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 180000, "TasksLoadingActivationBound": 3, "TasksLoadingBatchSize": 10}, "WebFormProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 100, "FailedSpinDelayInMs": 2000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 5, "NoTaskDelayMaxInMs": 10}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:00:55", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "NotificationHubSettings": {"HubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 5, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 180000, "TasksLoadingActivationBound": 0, "TasksLoadingBatchSize": 10}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 100, "FailedSpinDelayInMs": 2000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 5, "NoTaskDelayMaxInMs": 10, "MaxProcessingAttemptsCount": 5}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:00:55", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "SynchronizationWithLegacyHubSettings": {"HashingHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 5, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 180000, "TasksLoadingActivationBound": 3, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 5, "FailedSpinDelayInMs": 1000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 5, "NoTaskDelayMaxInMs": 10, "MaxProcessingAttemptsCount": 10}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:00:55", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 10, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "EBayHubSettings": {"HubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 5, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 180000, "TasksLoadingActivationBound": 3, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 5, "FailedSpinDelayInMs": 1000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 5, "NoTaskDelayMaxInMs": 10, "MaxProcessingAttemptsCount": 10}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:00:55", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 10, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}}, "WebFormProcessingSettings": {"NumberOfAttemptsToProcess": 5}, "SlackApiConfiguration": {"EmailFrom": "<EMAIL>", "EmailsTo": ["<EMAIL>"]}}