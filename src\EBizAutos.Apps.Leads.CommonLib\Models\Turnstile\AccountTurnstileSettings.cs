using System.Collections.Generic;

namespace EBizAutos.Apps.Leads.CommonLib.Models.Turnstile {
    public enum TurnstileFailureActionEnum {
        RejectOnFormSubmission = 0,
        AcceptAndGoToSpam = 1
    }

    public class AccountLeadTurnstileSettings {
        public AccountLeadTurnstileSettings() {
            LeadTypeSettings = new List<TurnstileLeadTypeSpecificSetting>();
            IsTurnstileEnabled = false;
            FailureAction = TurnstileFailureActionEnum.RejectOnFormSubmission;
            FailureMessage = "Turnstile validation failed. Please try again.";
        }

        public bool IsTurnstileEnabled { get; set; }
        public string SiteKey { get; set; }
        public string SecretKey { get; set; }
        public string FailureMessage { get; set; }
        public TurnstileFailureActionEnum FailureAction { get; set; }
        public List<TurnstileLeadTypeSpecificSetting> LeadTypeSettings { get; set; }
    }
}
