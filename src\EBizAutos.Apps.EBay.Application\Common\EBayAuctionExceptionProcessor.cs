﻿using System;
using System.Threading.Tasks;
using EBizAutos.Apps.EBay.Application.Commands;
using EBizAutos.Apps.EBay.Application.Commands.ErrorNotifications;
using EBizAutos.Apps.EBay.Application.EBay;
using EBizAutos.Apps.EBay.Application.EBay.Exceptions;
using EBizAutos.CommonLib.ServiceBus;
using EBizAutos.CommonLibCore;

namespace EBizAutos.Apps.EBay.Application.Common {
	public class EBayAuctionExceptionProcessor {
		private const string ConstInvalidEncyptedExceptionMessage = "The input data is not a complete block.";

		private readonly IServiceBusSender _serviceBusSender = null;

		public EBayAuctionExceptionProcessor(IServiceBusSender serviceBusSender) {
			_serviceBusSender = serviceBusSender;
		}

		public async Task<PromiseResult<bool>> Process(Exception ex, Action<Exception> exceptionHandler, string correlationId) {
			EBayApiException apiException = null;
			if (ex is EBayApiException eBayApiException) {
				apiException = eBayApiException;
			} else if (ex.InnerException is EBayApiException eBayApiInnerException) {
				apiException = eBayApiInnerException;
			}
			
			if (apiException != null) {
				EBayApiItemException apiItemException = apiException as EBayApiItemException;
				switch (apiException.ExceptionType) {
					case EBayApiException.ExceptionTypeEnum.TokenRevoked:
					case EBayApiException.ExceptionTypeEnum.TokenExpired:
					case EBayApiException.ExceptionTypeEnum.TokenInvalid:
						await _serviceBusSender.SendAsync<MarkEBayUserTokenExpiredCommand, MarkEBayUserTokenExpiredCommand>(
							new MarkEBayUserTokenExpiredCommand() {
								AccountId = apiException.AccountId,
								ContactId = apiException.ContactId,
								AuthToken = apiException.AuthToken,
								CorrelationId = correlationId
							}
						);
						return PromiseResult<bool>.Done(true);
					case EBayApiException.ExceptionTypeEnum.InvalidOAuth2RefreshToken:
						await _serviceBusSender.SendAsync<MarkOAuth2RefreshTokenExpiredCommand, MarkOAuth2RefreshTokenExpiredCommand>(
							new MarkOAuth2RefreshTokenExpiredCommand() {
								AccountId = apiException.AccountId,
								ContactId = apiException.ContactId,
								CorrelationId = correlationId
							}
						);
						return PromiseResult<bool>.Done(true);
					case EBayApiException.ExceptionTypeEnum.UserSuspended:
						await _serviceBusSender.SendAsync<NotifyDealerEBayUserSuspendedCommand, NotifyDealerEBayUserSuspendedCommand>(
							new NotifyDealerEBayUserSuspendedCommand() {
								AccountId = apiException.AccountId,
								ContactId = apiException.ContactId,
								CorrelationId = correlationId
							}
						);
						return PromiseResult<bool>.Done(true);
					case EBayApiException.ExceptionTypeEnum.AccountBlocked:
						await _serviceBusSender.SendAsync<NotifyDealerAccountBlockedCommand, NotifyDealerAccountBlockedCommand>(
							new NotifyDealerAccountBlockedCommand() {
								AccountId = apiException.AccountId,
								ContactId = apiException.ContactId,
								CorrelationId = correlationId
							}
						);
						break;
					case EBayApiException.ExceptionTypeEnum.ActivityLimitsExceeded:
						await _serviceBusSender.SendAsync<NotifyDealerActivityLimitsExceededCommand, NotifyDealerActivityLimitsExceededCommand>(
							new NotifyDealerActivityLimitsExceededCommand() {
								AccountId = apiException.AccountId,
								ContactId = apiException.ContactId,
								CorrelationId = correlationId
							}
						);
						break;
					case EBayApiException.ExceptionTypeEnum.BuyItNowNotAllowed:
						await _serviceBusSender.SendAsync<NotifyDealerBuyItNowNotAllowedCommand, NotifyDealerBuyItNowNotAllowedCommand>(
							new NotifyDealerBuyItNowNotAllowedCommand() {
								AccountId = apiException.AccountId,
								ContactId = apiException.ContactId,
								CorrelationId = correlationId
							}
						);
						break;
					case EBayApiException.ExceptionTypeEnum.NoCreditCardOnAccount:
						await _serviceBusSender.SendAsync<NotifyDealerCreditCardNotFoundCommand, NotifyDealerCreditCardNotFoundCommand>(
							new NotifyDealerCreditCardNotFoundCommand() {
								AccountId = apiException.AccountId,
								ContactId = apiException.ContactId,
								CorrelationId = correlationId
							}
						);
						break;
					case EBayApiException.ExceptionTypeEnum.CreditCardOnFileRequired:
						await _serviceBusSender.SendAsync<NotifyDealerCreditCardOnFileRequiredCommand, NotifyDealerCreditCardOnFileRequiredCommand>(
							new NotifyDealerCreditCardOnFileRequiredCommand() {
								AccountId = apiException.AccountId,
								ContactId = apiException.ContactId,
								CorrelationId = correlationId
							}
						);
						break;
					case EBayApiException.ExceptionTypeEnum.FixedPriceAuctionsListingDenied:
						await _serviceBusSender.SendAsync<NotifyDealerFixedPriceAuctionsListingDeniedCommand, NotifyDealerFixedPriceAuctionsListingDeniedCommand>(
							new NotifyDealerFixedPriceAuctionsListingDeniedCommand() {
								AccountId = apiException.AccountId,
								ContactId = apiException.ContactId,
								CorrelationId = correlationId
							}
						);
						break;
					case EBayApiException.ExceptionTypeEnum.IneligibleImmediatePayPalAccount:
						if (apiItemException != null) {
							await _serviceBusSender.SendAsync<NotifyDealerIneligibleImmediatePayPalAccountCommand, NotifyDealerIneligibleImmediatePayPalAccountCommand>(
								new NotifyDealerIneligibleImmediatePayPalAccountCommand() {
									AccountId = apiItemException.AccountId,
									ContactId = apiItemException.ContactId,
									Vin = apiItemException.Vin,
									CorrelationId = correlationId
								}
							);
						}

						break;
					case EBayApiException.ExceptionTypeEnum.JavaScriptNotAllowed:
						if (apiItemException != null) {
							await _serviceBusSender.SendAsync<NotifyDealerJavascriptNotAllowedCommand, NotifyDealerJavascriptNotAllowedCommand>(
								new NotifyDealerJavascriptNotAllowedCommand() {
									AccountId = apiItemException.AccountId,
									ContactId = apiItemException.ContactId,
									Vin = apiItemException.Vin,
									CorrelationId = correlationId
								}
							);
						}

						break;
					case EBayApiException.ExceptionTypeEnum.LocalAuctionsListingDenied:
						await _serviceBusSender.SendAsync<NotifyDealerLocalAuctionsListingDeniedCommand, NotifyDealerLocalAuctionsListingDeniedCommand>(
							new NotifyDealerLocalAuctionsListingDeniedCommand() {
								AccountId = apiException.AccountId,
								ContactId = apiException.ContactId,
								CorrelationId = correlationId
							}
						);
						break;
					case EBayApiException.ExceptionTypeEnum.NoSellerAccount:
						await _serviceBusSender.SendAsync<NotifyDealerNoSellerAccountCommand, NotifyDealerNoSellerAccountCommand>(
							new NotifyDealerNoSellerAccountCommand() {
								AccountId = apiException.AccountId,
								ContactId = apiException.ContactId,
								CorrelationId = correlationId
							}
						);
						break;
					case EBayApiException.ExceptionTypeEnum.PaymentMethodUpdateRequired:
						await _serviceBusSender.SendAsync<NotifyDealerPaymentMethodUpdateRequiredCommand, NotifyDealerPaymentMethodUpdateRequiredCommand>(
							new NotifyDealerPaymentMethodUpdateRequiredCommand() {
								AccountId = apiException.AccountId,
								ContactId = apiException.ContactId,
								CorrelationId = correlationId
							}
						);
						break;
					case EBayApiException.ExceptionTypeEnum.RegistrationBlocked:
						await _serviceBusSender.SendAsync<NotifyDealerRegistrationBlockedCommand, NotifyDealerRegistrationBlockedCommand>(
							new NotifyDealerRegistrationBlockedCommand() {
								AccountId = apiException.AccountId,
								ContactId = apiException.ContactId,
								CorrelationId = correlationId
							}
						);
						break;
					case EBayApiException.ExceptionTypeEnum.SellerWithOverdueLimit:
						await _serviceBusSender.SendAsync<NotifyDealerWithOverdueLimitCommand, NotifyDealerWithOverdueLimitCommand>(
							new NotifyDealerWithOverdueLimitCommand() {
								AccountId = apiException.AccountId,
								ContactId = apiException.ContactId,
								CorrelationId = correlationId
							}
						);
						break;
					case EBayApiException.ExceptionTypeEnum.ItemNotFound:
					case EBayApiException.ExceptionTypeEnum.Unauthorized:
					case EBayApiException.ExceptionTypeEnum.InvalidUserId:
						return PromiseResult<bool>.Done(true); //suppress these types of exception
					default:
						exceptionHandler(ex);
						return PromiseResult<bool>.Failed(ex);
				}
			} else if (ex.Message.Contains(ConstInvalidEncyptedExceptionMessage)) {
				exceptionHandler(ex);

				return PromiseResult<bool>.Done(true);
			}

			exceptionHandler(ex);
			return PromiseResult<bool>.Failed(ex);
		}
	}
}
