﻿using System;

namespace EBizAutos.Apps.Monitor.MongoDBRepository {
	internal static class InternalConstants {
		public static class CollectionNames {
			public const string EmailTasksCollection = "*QueueTaskEmailCollection";
			public const string BrandExportReportsTasksCollection = "*BrandExportReportsTasksCollection";
			public const string BrandExportsCollection = "BrandExportsCollection";
			public const string ConstSynchronizationTrackingSettingsCollection = "SynchronizationTrackingSettingsCollection";
			public const string ConstAnalyticsAccountSettingsCollection = "AppsAnalyticsAccountSettingsCollection";
			public static IGoogleAnalyticsCollectionNames GoogleAnalyticsUniversal => GoogleAnalyticsUniversalCollectionNames.Instance;
			public static IGoogleAnalyticsCollectionNames GoogleAnalytics4 => GoogleAnalytics4CollectionNames.Instance;

			private class GoogleAnalyticsUniversalCollectionNames : IGoogleAnalyticsCollectionNames {
				public string RebuildTasksCollection => "*AnalyticsTasksCollection";
				public string PaidSearchReportsCollection => "PaidSearchReportsCollection";
				public string AnalyticsTrackingSettingsCollection => "ServiceSchedulerInfoCollection";

				private GoogleAnalyticsUniversalCollectionNames() {
				}

				public static GoogleAnalyticsUniversalCollectionNames Instance => new GoogleAnalyticsUniversalCollectionNames();
			}

			private class GoogleAnalytics4CollectionNames : IGoogleAnalyticsCollectionNames {
				public string RebuildTasksCollection => "*GA4AnalyticsTasksCollection";
				public string PaidSearchReportsCollection => "GA4PaidSearchReportsCollection";
				public string AnalyticsTrackingSettingsCollection => "GA4ServiceSchedulerInfoCollection";

				private GoogleAnalytics4CollectionNames() {
				}

				public static GoogleAnalytics4CollectionNames Instance => new GoogleAnalytics4CollectionNames();
			}
		}
	}
}
