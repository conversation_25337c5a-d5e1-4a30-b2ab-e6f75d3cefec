# Cloudflare Turnstile Settings Implementation

## Огляд

Цей документ описує реалізацію функціоналу для налаштування Cloudflare Turnstile сетінгів для кожного акаунта в лідс арі. Функціонал дозволяє як адміністраторам, так і дилерам налаштовувати ці сетінги.

## Ключові особливості

✅ **Окремий таб для управління** - Turnstile сетінги менеджаться на окремому табі, а не спільно з іншими лід сетінгами  
✅ **Збереження існуючих даних** - Коли turnstile нод приходить порожнім, існуючі значення в базі не перезатираються  
✅ **Рольовий доступ** - Різні права доступу для адміністраторів та дилерів  
✅ **Налаштування по типу лідів** - Можливість налаштування для кожного типу лідів окремо  

## Додані файли

### Моделі
- `src/EBizAutos.Apps.Leads.Api/Models/AccountLeadSettings/ViewModelAccountLeadTurnstileSettingsAdmin.cs`
- `src/EBizAutos.Apps.Leads.Api/Models/AccountLeadSettings/ViewModelAccountLeadTurnstileSettingsDealer.cs`

### Валідатори
- `src/EBizAutos.Apps.Leads.Api/ModelValidators/AccountLeadTurnstileSettingsAdminValidator.cs`
- `src/EBizAutos.Apps.Leads.Api/ModelValidators/AccountLeadTurnstileSettingsDealerValidator.cs`

### Тести
- `src/EBizAutos.Apps.Leads.Api.Tests/Controllers/AccountLeadTurnstileSettingsControllerTests.cs`

### Документація
- `docs/Cloudflare-Turnstile-API-Documentation.md`

## Модифіковані файли

### Контролер
- `src/EBizAutos.Apps.Leads.Api/Controllers/Api/AccountLeadSettingsController.cs`
  - Додано 4 нові endpoints для роботи з Turnstile сетінгами

### Менеджер
- `src/EBizAutos.Apps.Leads.Api/Managers/AccountLeadSettingsManager.cs`
  - Додано методи для отримання та оновлення Turnstile сетінгів
  - Додано збереження існуючих Turnstile сетінгів при оновленні інших сетінгів

## API Endpoints

### Для адміністраторів
```
GET  /admin/accounts/{accountId}/turnstile-settings
POST /admin/accounts/{accountId}/turnstile-settings
```

### Для дилерів
```
GET  /dealer/accounts/{accountId}/turnstile-settings
POST /dealer/accounts/{accountId}/turnstile-settings
```

## Різниця між Admin та Dealer моделями

| Поле | Admin | Dealer | Примітка |
|------|-------|--------|----------|
| AccountId | ✅ | ✅ | |
| IsTurnstileEnabled | ✅ | ✅ | |
| SiteKey | ✅ | ❌ | Тільки для адміна |
| SecretKey | ✅ | ❌ | Тільки для адміна |
| FailureMessage | ✅ | ✅ | |
| FailureAction | ✅ | ✅ | |
| LeadTypeSettings | ✅ | ✅ | |

## Валідація

### Для адміністраторів
- `SiteKey` обов'язковий, якщо `IsTurnstileEnabled = true`
- `SecretKey` обов'язковий, якщо `IsTurnstileEnabled = true`
- `FailureMessage` не може перевищувати 500 символів

### Для дилерів
- `FailureMessage` не може перевищувати 500 символів

## Тестування

Запустіть тести для перевірки функціоналу:

```bash
dotnet test src/EBizAutos.Apps.Leads.Api.Tests/EBizAutos.Apps.Leads.Api.Tests.csproj
```

## Збірка проекту

```bash
dotnet build src/EBizAutos.Apps.Leads.Api/EBizAutos.Apps.Leads.Api.csproj
```

## Приклад використання

### Отримання сетінгів (Admin)
```http
GET /admin/accounts/123/turnstile-settings
Authorization: Bearer {token}
```

### Оновлення сетінгів (Admin)
```http
POST /admin/accounts/123/turnstile-settings
Content-Type: application/json
Authorization: Bearer {token}

{
  "accountId": 123,
  "isTurnstileEnabled": true,
  "siteKey": "0x4AAAAAAABkMYinukE_q1SI",
  "secretKey": "0x4AAAAAAABkMYinutE_q1SJ",
  "failureMessage": "Turnstile validation failed. Please try again.",
  "failureAction": 0,
  "leadTypeSettings": [
    {
      "leadType": 1,
      "isActive": true
    }
  ]
}
```

## Важливі примітки

1. **Безпека**: Дилери не мають доступу до `SiteKey` та `SecretKey`
2. **Збереження даних**: При оновленні звичайних лід сетінгів, Turnstile сетінги зберігаються
3. **Автореєстрація валідаторів**: FluentValidation автоматично реєструє нові валідатори
4. **Окреме управління**: Turnstile сетінги управляються незалежно від інших сетінгів

## Статус

✅ **Завершено**: Всі основні функції реалізовані та протестовані  
✅ **Тести**: Всі тести проходять успішно  
✅ **Документація**: Створена повна документація API  
✅ **Валідація**: Додана валідація для всіх моделей  

Функціонал готовий до використання!
