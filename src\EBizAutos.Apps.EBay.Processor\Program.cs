﻿using System;
using System.IO;
using System.Threading.Tasks;
using EBizAutos.ApplicationCommonLib.Applications.Amazon.ParameterStore;
using EBizAutos.Apps.EBay.Processor.Controllers;
using EBizAutos.Apps.EBay.Processor.Extensions;
using EBizAutos.Core.ServiceCommonLib.Service;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Hosting.Internal;

namespace EBizAutos.Apps.EBay.Processor {
	class Program {
		public static async Task Main(string[] args) {
			IHostingEnvironment hostingEnvironment = new HostingEnvironment {
				EnvironmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"),
				ApplicationName = AppDomain.CurrentDomain.FriendlyName,
				ContentRootPath = AppDomain.CurrentDomain.BaseDirectory,
				ContentRootFileProvider = new PhysicalFileProvider(AppDomain.CurrentDomain.BaseDirectory)
			};
			Console.WriteLine($"Environment: {hostingEnvironment.EnvironmentName}");

			string applicationName = hostingEnvironment.IsDevelopment() ? AppDomain.CurrentDomain.FriendlyName : new DirectoryInfo(hostingEnvironment.ContentRootPath).Name;

			#region Configuration
			IConfigurationRoot configuration = new ConfigurationBuilder()
				.AddJsonFile($"appsettings.{hostingEnvironment.EnvironmentName}.json", optional: false, reloadOnChange: true)
				.AddJsonFile($"appsettings.{hostingEnvironment.EnvironmentName}.serilog.json", optional: false, reloadOnChange: true)
				.AddAwsConfigurationParameters(applicationName)
				.AddEnvironmentVariables()
				.AddCommandLine(args)
				.Build();
			#endregion

			#region Dependencies
			IServiceCollection services = new ServiceCollection();
			services.RegisterCustomServices(configuration, hostingEnvironment);
			#endregion

			ServiceConfiguration serviceConfiguration = new ServiceConfiguration(
				"eBizAutosAppsEBayService",
				"eBizAutos Apps EBay Service",
				"Service listens to service bus events to fill up ebay data and process eBay bids",
				"EBayService"
			);

			await Service.RunAsync<EBayServiceActorSystemController>(serviceConfiguration, services);
		}
	}
}
