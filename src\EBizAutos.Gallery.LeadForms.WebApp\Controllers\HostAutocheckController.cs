﻿using System;
using EBizAutos.CommonLib.Exceptions;
using EBizAutos.CommonLib.PublicConstants;
using EBizAutos.FoundationCommonLib.Abstract.Repositories.Foundation;
using EBizAutos.FoundationCommonLib.Abstract.Repositories.Gallery;
using EBizAutos.FoundationCommonLib.Models.Common.SiteSettings;
using EBizAutos.Gallery.LeadForms.WebApp.Factories;
using EBizAutos.Gallery.LeadForms.WebApp.Providers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace EBizAutos.Gallery.LeadForms.WebApp.Controllers {
	[Route("leads")]
	public class HostAutocheckController : BaseLeadsController {
		private readonly SiteboxSettingsProvider _siteboxSettingsProvider = null;
		private readonly ISiteboxServiceFactory<ISiteSettingsRepository> _siteSettingsRepositoryFactory = null;
		private readonly ISiteboxServiceFactory<IGalleryVehicleRepository> _vehicleRepositoryFactory = null;

		public HostAutocheckController(AppConfiguration appConfiguration, ExceptionHandler exceptionHandler,
									SiteboxSettingsProvider siteboxSettingsProvider,
									ISiteboxServiceFactory<ISiteSettingsRepository> siteSettingsRepositoryFactory,
									ISiteboxServiceFactory<IGalleryVehicleRepository> vehicleRepositoryFactory) : base(appConfiguration, exceptionHandler) {
			_siteboxSettingsProvider = siteboxSettingsProvider;
			_siteSettingsRepositoryFactory = siteSettingsRepositoryFactory;
			_vehicleRepositoryFactory = vehicleRepositoryFactory;
		}

		[Route("autocheckcontext.aspx")]
		public IActionResult Index(string vin, string sitebox) {
			if (string.IsNullOrEmpty(sitebox)) {
				return NotFound("Page not found.Invalid sitebox parameter.");
			}

			if (string.IsNullOrEmpty(vin)) {
				return NotFound("Page not found.");
			}

			sitebox = sitebox.ToLower();
			string host = HttpContext.Request.Host.HasValue ? HttpContext.Request.Host.Value.Replace(Constants.ConstWWWPrefix, "") : "";

			SiteboxSettings siteboxSettings = _siteboxSettingsProvider.GetSiteboxSettings(sitebox);
			if (siteboxSettings == null) {
				return NotFound($"Page not found.Invalid {sitebox} sitebox.");
			}

			ISiteSettingsRepository siteSettingsRepository = _siteSettingsRepositoryFactory.Create(siteboxSettings);
			SiteSettings siteSettings = siteSettingsRepository.GetSiteSettings(host);
			if (siteSettings == null) {
				return NotFound("Page not found.Site settings is null or empty.");
			}

			IGalleryVehicleRepository vehicleRepository = _vehicleRepositoryFactory.Create(siteboxSettings);
			string template = vehicleRepository.GetVehicleAutocheckTemplate(siteSettings.SiteId, vin);
			if (string.IsNullOrEmpty(template)) {
				return NotFound("Autocheck report is not found.");
			}

			return Content(template, Constants.ConstContentTypeHtml);
		}
	}
}
