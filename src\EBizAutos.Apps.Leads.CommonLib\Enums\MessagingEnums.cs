using System.ComponentModel;

namespace EBizAutos.Apps.Leads.CommonLib.Enums {
	public class MessagingEnums {
		public enum SynchronizationOperationTypeEnum : byte {
			[Description("Communication insert")]
			CommunicationInsert = 1,
			[Description("Communication update")]
			CommunicationUpdate = 2,
			[Description("Communication delete")]
			CommunicationDelete = 3,
			[Description("Campaign type insert")]
			CampaignTypeInsert = 4,
			[Description("Campaign type delete")]
			CampaignTypeDelete = 5,
			[Description("Conversation details notification upsert")]
			ConversationDetailsNotificationsUpsert = 6,
			[Description("Conversation upsert")]
			ConversationUpsert = 7,
			[Description("Conversation delete")]
			ConversationDelete = 8,
			[Description("Conversation details upsert")]
			ConversationDetailsUpsert = 9,
			[Description("Communication statistic upsert")]
			CommunicationStatisticUpsert = 10,
			[Description("Account statistic upsert")]
			AccountStatisticUpsert = 11,
			[Description("Campaign type update")]
			CampaignTypeUpdate = 12,
			[Description("Phone History Upsert")]
			PhoneHistoryUpsert = 13,
			[Description("CreditApp Notification upsert")]
			CreditAppNotificationUpsert = 14,
			[Description("Conversation details delete")]
			ConversationDetailsDelete = 15,
			[Description("User email template create")]
			UserEmailTemplateInsert = 16,
			[Description("User email template update")]
			UserEmailTemplateUpdate = 17,
			[Description("User email template delete")]
			UserEmailTemplateDelete = 18,
			[Description("Account lead settings update")]
			AccountLeadSettingsUpdate = 19
		};
	}
}
