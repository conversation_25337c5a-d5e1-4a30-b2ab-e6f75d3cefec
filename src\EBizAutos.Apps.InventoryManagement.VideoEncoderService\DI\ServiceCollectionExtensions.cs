﻿using EBizAutos.Apps.ServiceBus.Consumers.Tests;
using EBizAutos.Apps.ServiceBus.Events.Tests;
using EBizAutos.CommonLib.Exceptions;
using EBizAutos.CommonLib.ServiceBus;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace EBizAutos.Apps.InventoryManagement.VideoEncoderService.DI {
	public static class ServiceCollectionExtensions {
		internal static AppConfiguration RegisterCustomServices(this IServiceCollection serviceCollection, IConfiguration configuration, IHostingEnvironment hostingEnvironment) {
			AppConfiguration appConfiguration = new AppConfiguration();
			configuration.Bind(appConfiguration);
			serviceCollection.AddSingleton<AppConfiguration>(appConfiguration);
			serviceCollection.AddSingleton<IHostingEnvironment>(hostingEnvironment);
			serviceCollection.AddSingleton<ExceptionHandler>(sp=> new ExceptionHandler(appConfiguration.ExceptionSettings, appConfiguration.AppSettings.ApplicationName, sp));
			serviceCollection.AddMemoryCache();

			serviceCollection.RegisterActorSystemServices(configuration);
			serviceCollection.RegisterDbServices(appConfiguration);
			serviceCollection.RegisterConfiguration(configuration);
			serviceCollection.RegisterPhotoToVideoServices(appConfiguration);
			serviceCollection.AddTransient<IConsumer<ITestErrorEvent>, TestErrorEventConsumer>(
				sp => new TestErrorEventConsumer(sp.GetRequiredService<ExceptionHandler>(), appConfiguration.AppSettings.ApplicationName)
			);
			serviceCollection.RegisterServiceBus(appConfiguration, hostingEnvironment);
			serviceCollection.RegisterSerilogLogger(appConfiguration, hostingEnvironment);
			return appConfiguration;
		}
	}
}
