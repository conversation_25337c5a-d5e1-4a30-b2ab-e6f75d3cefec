using System;
using System.Collections.Generic;
using System.Linq;
using EBizAutos.Apps.Leads.Api.Models.AccountLeadSettings;
using EBizAutos.Apps.Leads.CommonLib.Abstract.Repositories;
using EBizAutos.Apps.Leads.CommonLib.Enums;
using EBizAutos.Apps.Leads.CommonLib.Models;
using EBizAutos.Apps.Leads.CommonLib.Models.Fees;
using EBizAutos.Apps.Leads.CommonLib.Models.Turnstile;
using EBizAutos.Apps.Leads.CommonLib.ServiceBus.Events;
using EBizAutos.Apps.ServiceBus.Events.Lead;
using EBizAutos.CommonLib.Extensions;
using EBizAutos.CommonLib.ServiceBus;
using EBizAutos.CommonLibCore;
using Microsoft.Extensions.DependencyInjection;

namespace EBizAutos.Apps.Leads.Api.Managers {
	internal sealed class AccountLeadSettingsManager : BaseConverterManager {
		private readonly IAppsLeadsSettingsRepository _leadsSettingsRepository = null;
		private readonly IAppsAccountLeadSettingsRepository _accountLeadSettingsRepository = null;
		private readonly IAppsLeadsDataSynchronizationTaskRepository _synchronizationRepository = null;
		private readonly IServiceBusPublisher _serviceBusPublisher = null;

		public AccountLeadSettingsManager(IServiceProvider serviceProvider) : base(serviceProvider) {
			_leadsSettingsRepository = serviceProvider.GetService<IAppsLeadsSettingsRepository>();
			_accountLeadSettingsRepository = serviceProvider.GetService<IAppsAccountLeadSettingsRepository>();
			_synchronizationRepository = serviceProvider.GetService<IAppsLeadsDataSynchronizationTaskRepository>();
			_serviceBusPublisher = serviceProvider.GetService<IServiceBusPublisher>();
		}

		public PromiseResult<ViewModelAccountLeadSettingsAdmin> GetAdminSettingsViewModel(int accountId) {
			ViewModelAccountLeadSettingsAdmin viewModel = new ViewModelAccountLeadSettingsAdmin();

			try {
				AccountLeadSettings dbAccountLeadSettings = _accountLeadSettingsRepository.GetAccountLeadSettings(accountId);

				if (dbAccountLeadSettings == null) {
					// default new leads settings
					viewModel.AccountId = accountId;
					viewModel.IsActive = false;
					viewModel.HasCustomFees = false;
					viewModel.HasToUsePaidUsers = false;
				} else {
					viewModel = GetAdminSettingsViewModel(dbAccountLeadSettings);
				}

				if (!viewModel.HasCustomFees) {
					LeadsSystemSettings dbLeadsSystemSettings = _leadsSettingsRepository.GetLeadsSystemSettings();

					if (dbLeadsSystemSettings?.FeesForTwilio != null) {
						viewModel.TwilioCallFee = dbLeadsSystemSettings.FeesForTwilio.CallFee;
						viewModel.TwilioSMSFee = dbLeadsSystemSettings.FeesForTwilio.SMSFee;
					}
				}

				return PromiseResult<ViewModelAccountLeadSettingsAdmin>.Done(viewModel);
			} catch (Exception ex) {
				ex.Data.Add("AccountId", accountId);
				return PromiseResult<ViewModelAccountLeadSettingsAdmin>.Failed(ex);
			}
		}

		public PromiseResult<ViewModelAccountLeadSettingsDealer> GetDealerSettingsViewModel(int accountId) {
			ViewModelAccountLeadSettingsDealer viewModel = new ViewModelAccountLeadSettingsDealer();

			try {
				AccountLeadSettings dbAccountLeadSettings = _accountLeadSettingsRepository.GetAccountLeadSettings(accountId);
				if (dbAccountLeadSettings == null) {
					return PromiseResult<ViewModelAccountLeadSettingsDealer>.Rejected("Account Leads Settings is null or empty");
				}

				ViewModelAccountLeadSettingsDealer vwSettings = new ViewModelAccountLeadSettingsDealer {
					AccountId = dbAccountLeadSettings.AccountId,
					IsActive = dbAccountLeadSettings.IsActive,
					// Lead Management Settings
					HasToArchiveLeadMessages = dbAccountLeadSettings.ManagementSettings.HasToArchiveLeadMessages,
					ArchiveLeadMessagesAfterInDays = dbAccountLeadSettings.ManagementSettings.ArchiveLeadMessagesAfterInDays,
					HasToDeleteArchivedLeadMessages = dbAccountLeadSettings.ManagementSettings.HasToDeleteArchivedLeadMessages,
					DeleteArchivedLeadMessagesAfterInDays = dbAccountLeadSettings.ManagementSettings.DeleteArchivedLeadMessagesAfterInDays,
					// Lead Summary Report settings
					HasToSendDailyReports = dbAccountLeadSettings.SummaryReportSettings.HasToSendDailyReports,
					ReportRecipientEmails = dbAccountLeadSettings.SummaryReportSettings.ReportRecipientEmails,
					ReportDeliveryTime = dbAccountLeadSettings.SummaryReportSettings.ReportDeliveryTime
				};

				return PromiseResult<ViewModelAccountLeadSettingsDealer>.Done(vwSettings);
			} catch (Exception ex) {
				ex.Data.Add("AccountId", accountId);
				return PromiseResult<ViewModelAccountLeadSettingsDealer>.Failed(ex);
			}
		}
		
		public PromiseResult<ViewModelAccountLeadTurnstileSettings> GetTurnstileSettingsViewModel(int accountId) {
			try {
				AccountLeadSettings dbAccountLeadSettings = _accountLeadSettingsRepository.GetAccountLeadSettings(accountId);
				if (dbAccountLeadSettings == null) {
					return PromiseResult<ViewModelAccountLeadTurnstileSettings>.Rejected("Account Leads Settings is null or empty");
				}

				ViewModelAccountLeadTurnstileSettings viewModel;
				if (dbAccountLeadSettings.TurnstileSettings == null) {
					// default new turnstile settings
					viewModel = new ViewModelAccountLeadTurnstileSettings {
						AccountId = accountId,
						IsTurnstileEnabled = false,
						FailureAction = TurnstileFailureActionEnum.RejectOnFormSubmission,
						FailureMessage = "Turnstile validation failed. Please try again."
					};
				} else {
					viewModel = GetTurnstileSettingsViewModel(dbAccountLeadSettings.TurnstileSettings, accountId);
				}

				return PromiseResult<ViewModelAccountLeadTurnstileSettings>.Done(viewModel);
			} catch (Exception ex) {
				ex.Data.Add("AccountId", accountId);
				return PromiseResult<ViewModelAccountLeadTurnstileSettings>.Failed(ex);
			}
		}

		public PromiseResult<AccountLeadSettings> UpdateAccountLeadSettings(int accountId, ViewModelAccountLeadSettingsAdmin settings,
																			string logId, out AccountLeadSettings originalSettings) {
			try {
				originalSettings = _accountLeadSettingsRepository.GetAccountLeadSettings(accountId);

				AccountLeadSettings settingsToUpsert = GetAccountLeadSettingsDataModel(settings, originalSettings);

				AccountLeadSettings upsertSettings = null;

				if (originalSettings != null) {//only on update
					upsertSettings = _accountLeadSettingsRepository.UpdateAccountLeadSettings(settingsToUpsert);

					CheckAndPublishIfShiftDigitalWasChanged(originalSettings, upsertSettings);

					_synchronizationRepository.SendTask(
						logId,
						originalSettings.AccountId.ToString(),
						originalSettings.AccountId,
						MessagingEnums.SynchronizationOperationTypeEnum.AccountLeadSettingsUpdate
					);
				} else {
					upsertSettings = _accountLeadSettingsRepository.InsertAccountLeadSettings(settingsToUpsert);
					_serviceBusPublisher.PublishAsync<IAccountShiftDigitalSettingsUpdatedEvent, AccountShiftDigitalSettingsUpdatedEvent>
						(new AccountShiftDigitalSettingsUpdatedEvent { SiteId = settingsToUpsert.AccountId });
				}

				return PromiseResult<AccountLeadSettings>.Done(upsertSettings);
			} catch (Exception ex) {
				originalSettings = null;
				ex.Data.Add("AccountId", accountId);
				return PromiseResult<AccountLeadSettings>.Failed(ex);
			}
		}

		public PromiseResult<AccountLeadSettings> UpdateAccountLeadSettings(int accountId, ViewModelAccountLeadSettingsDealer settings,
																			string logId, out AccountLeadSettings originalSettings) {
			try {
				originalSettings = _accountLeadSettingsRepository.GetAccountLeadSettings(accountId);
				if (originalSettings == null) {
					return PromiseResult<AccountLeadSettings>.Rejected("Account Leads Settings is null or empty");
				}

				AccountLeadSettings settingsToUpsert = originalSettings.DeepCopyForceNet();

				// Lead Management Settings
				settingsToUpsert.ManagementSettings.HasToArchiveLeadMessages = settings.HasToArchiveLeadMessages;
				settingsToUpsert.ManagementSettings.ArchiveLeadMessagesAfterInDays = settings.ArchiveLeadMessagesAfterInDays;
				settingsToUpsert.ManagementSettings.HasToDeleteArchivedLeadMessages = settings.HasToDeleteArchivedLeadMessages;
				settingsToUpsert.ManagementSettings.DeleteArchivedLeadMessagesAfterInDays = settings.DeleteArchivedLeadMessagesAfterInDays;
				// Lead Summary Report settings
				settingsToUpsert.SummaryReportSettings.HasToSendDailyReports = settings.HasToSendDailyReports;
				settingsToUpsert.SummaryReportSettings.ReportRecipientEmails = settings.ReportRecipientEmails ?? new List<string>();
				settingsToUpsert.SummaryReportSettings.ReportDeliveryTime = settings.ReportDeliveryTime;

				AccountLeadSettings upsertSettings = _accountLeadSettingsRepository.UpdateAccountLeadSettings(settingsToUpsert);

				CheckAndPublishIfShiftDigitalWasChanged(originalSettings, upsertSettings);

				_synchronizationRepository.SendTask(
					logId,
					originalSettings.AccountId.ToString(),
					originalSettings.AccountId,
					MessagingEnums.SynchronizationOperationTypeEnum.AccountLeadSettingsUpdate
				);

				return PromiseResult<AccountLeadSettings>.Done(upsertSettings);
			} catch (Exception ex) {
				originalSettings = null;
				ex.Data.Add("AccountId", accountId);
				return PromiseResult<AccountLeadSettings>.Failed(ex);
			}
		}

		public PromiseResult<AccountLeadSettings> UpdateAccountLeadTurnstileSettings(int accountId, ViewModelAccountLeadTurnstileSettings settings,
																					string logId, out AccountLeadSettings originalSettings) {
			try {
				originalSettings = _accountLeadSettingsRepository.GetAccountLeadSettings(accountId);

				AccountLeadSettings settingsToUpsert = originalSettings != null ? originalSettings.DeepCopyForceNet() : new AccountLeadSettings { AccountId = accountId };

				// Update only Turnstile settings, preserve other settings
				settingsToUpsert.TurnstileSettings = GetAccountLeadTurnstileSettingsDataModel(settings);

				AccountLeadSettings upsertSettings = null;

				if (originalSettings != null) {
					upsertSettings = _accountLeadSettingsRepository.UpdateAccountLeadSettings(settingsToUpsert);

					_synchronizationRepository.SendTask(
						logId,
						originalSettings.AccountId.ToString(),
						originalSettings.AccountId,
						MessagingEnums.SynchronizationOperationTypeEnum.AccountLeadSettingsUpdate
					);
				} else {
					// Initialize default settings if account doesn't exist
					settingsToUpsert.IsActive = true;
					settingsToUpsert.NotificationSettings = new AccountLeadNotificationSettings() {
						ADFLeadSource = "eBizAutos"
					};
					upsertSettings = _accountLeadSettingsRepository.InsertAccountLeadSettings(settingsToUpsert);
				}

				return PromiseResult<AccountLeadSettings>.Done(upsertSettings);
			} catch (Exception ex) {
				originalSettings = null;
				ex.Data.Add("AccountId", accountId);
				return PromiseResult<AccountLeadSettings>.Failed(ex);
			}
		}

		private void CheckAndPublishIfShiftDigitalWasChanged(AccountLeadSettings originalSettings, AccountLeadSettings settingsToUpsert) {
			if (originalSettings.NotificationSettings.ShiftDigitalId != settingsToUpsert.NotificationSettings.ShiftDigitalId ||
				originalSettings.NotificationSettings.ShiftDigitalApiUrl != settingsToUpsert.NotificationSettings.ShiftDigitalApiUrl ||
				originalSettings.NotificationSettings.ShiftDigitalType != settingsToUpsert.NotificationSettings.ShiftDigitalType) {

				_serviceBusPublisher.PublishAsync<IAccountShiftDigitalSettingsUpdatedEvent, AccountShiftDigitalSettingsUpdatedEvent>(new AccountShiftDigitalSettingsUpdatedEvent { SiteId = settingsToUpsert.AccountId });
			}
		}

		private ViewModelAccountLeadSettingsAdmin GetAdminSettingsViewModel(AccountLeadSettings dbSettings) {
			ViewModelAccountLeadSettingsAdmin vwSettings = new ViewModelAccountLeadSettingsAdmin() {
				AccountId = dbSettings.AccountId,
				IsActive = dbSettings.IsActive,
				HasToUsePaidUsers = dbSettings.HasToUsePaidUsers,
				DealershipId = dbSettings.NotificationSettings.DealershipId,
				ShiftDigitalApiUrl = dbSettings.NotificationSettings.ShiftDigitalApiUrl,
				ShiftDigitalId = dbSettings.NotificationSettings.ShiftDigitalId,
				ShiftDigitalType = dbSettings.NotificationSettings.ShiftDigitalType,
				PremierTruckApiUrl = dbSettings.NotificationSettings.PremierTruckApiUrl,
				PremierTruckDbName = dbSettings.NotificationSettings.PremierTruckDbName,
				ADFLeadSource = dbSettings.NotificationSettings.ADFLeadSource,
				HasToAppendLeadTypeToADF = dbSettings.NotificationSettings.HasToAppendLeadTypeToADF,
				HasToShowProviderIdInADF = dbSettings.NotificationSettings.HasToShowProviderIdInADF,
				HasToShowEBaySourceInADF = dbSettings.NotificationSettings.HasToShowEBaySourceInADF,
				EBayPercentRequiredToNotifyDisplayedContact = dbSettings.NotificationSettings.EBayPercentRequiredToNotifyDisplayedContact,
				EBayPercentRequiredToNotifyCopyToEmail = dbSettings.NotificationSettings.EBayPercentRequiredToNotifyCopyToEmail,
				EBayReservePricePercentageRequiredToNotifyAdf = dbSettings.NotificationSettings.EBayReservePricePercentageRequiredToNotifyAdf
			};

			if (dbSettings.FeesForTwilio != null) {
				vwSettings.HasCustomFees = true;
				vwSettings.TwilioCallFee = dbSettings.FeesForTwilio.CallFee;
				vwSettings.TwilioSMSFee = dbSettings.FeesForTwilio.SMSFee;
			}

			// Lead Management Settings
			vwSettings.HasToArchiveLeadMessages = dbSettings.ManagementSettings.HasToArchiveLeadMessages;
			vwSettings.ArchiveLeadMessagesAfterInDays = dbSettings.ManagementSettings.ArchiveLeadMessagesAfterInDays;
			vwSettings.HasToDeleteArchivedLeadMessages = dbSettings.ManagementSettings.HasToDeleteArchivedLeadMessages;
			vwSettings.DeleteArchivedLeadMessagesAfterInDays = dbSettings.ManagementSettings.DeleteArchivedLeadMessagesAfterInDays;

			// Lead Summary Report settings
			vwSettings.HasToSendDailyReports = dbSettings.SummaryReportSettings.HasToSendDailyReports;
			vwSettings.ReportRecipientEmails = dbSettings.SummaryReportSettings.ReportRecipientEmails ?? new List<string>();
			vwSettings.ReportDeliveryTime = dbSettings.SummaryReportSettings.ReportDeliveryTime;

			return vwSettings;
		}

		private AccountLeadSettings GetAccountLeadSettingsDataModel(ViewModelAccountLeadSettingsAdmin viewModel, AccountLeadSettings originalSettings) {
			AccountLeadSettings dbAccountLeadSettings = originalSettings != null ? originalSettings.DeepCopyForceNet() : new AccountLeadSettings();
			dbAccountLeadSettings.AccountId = viewModel.AccountId;
			dbAccountLeadSettings.IsActive = viewModel.IsActive;
			dbAccountLeadSettings.HasToUsePaidUsers = viewModel.HasToUsePaidUsers;
			dbAccountLeadSettings.NotificationSettings = new AccountLeadNotificationSettings() {
				DealershipId = viewModel.DealershipId,
				ShiftDigitalApiUrl = viewModel.ShiftDigitalApiUrl,
				ShiftDigitalId = viewModel.ShiftDigitalId,
				ShiftDigitalType = viewModel.ShiftDigitalType,
				PremierTruckApiUrl = viewModel.PremierTruckApiUrl,
				PremierTruckDbName = viewModel.PremierTruckDbName,
				ADFLeadSource = viewModel.ADFLeadSource,
				HasToAppendLeadTypeToADF = viewModel.HasToAppendLeadTypeToADF,
				HasToShowProviderIdInADF = viewModel.HasToShowProviderIdInADF,
				HasToShowEBaySourceInADF = viewModel.HasToShowEBaySourceInADF,
				EBayPercentRequiredToNotifyDisplayedContact = viewModel.EBayPercentRequiredToNotifyDisplayedContact,
				EBayPercentRequiredToNotifyCopyToEmail = viewModel.EBayPercentRequiredToNotifyCopyToEmail,
				EBayReservePricePercentageRequiredToNotifyAdf = viewModel.EBayReservePricePercentageRequiredToNotifyAdf
			};
			
			if (dbAccountLeadSettings.ManagementSettings == null) {
				dbAccountLeadSettings.ManagementSettings = new AccountLeadManagementSettings();
			}

			// Preserve existing Turnstile settings when updating other settings
			if (originalSettings?.TurnstileSettings != null && dbAccountLeadSettings.TurnstileSettings == null) {
				dbAccountLeadSettings.TurnstileSettings = originalSettings.TurnstileSettings;
			}

			if (viewModel.HasCustomFees) {
				dbAccountLeadSettings.FeesForTwilio = new LeadFees() {
					CallFee = viewModel.TwilioCallFee,
					SMSFee = viewModel.TwilioSMSFee
				};
			} else {
				dbAccountLeadSettings.FeesForTwilio = null;
			}

			return dbAccountLeadSettings;
		}

		#region Turnstile Settings Helper Methods

		private ViewModelAccountLeadTurnstileSettings GetTurnstileSettingsViewModel(AccountLeadTurnstileSettings dbSettings, int accountId) {
			return new ViewModelAccountLeadTurnstileSettings {
				AccountId = accountId,
				IsTurnstileEnabled = dbSettings.IsTurnstileEnabled,
				SiteKey = dbSettings.SiteKey,
				SecretKey = dbSettings.SecretKey,
				FailureMessage = dbSettings.FailureMessage,
				FailureAction = dbSettings.FailureAction,
				LeadTypeSettings = dbSettings.LeadTypeSettings?.Select(x => new ViewModelTurnstileLeadTypeSpecificSetting {
					LeadType = x.LeadType,
					IsActive = x.IsActive
				}).ToList() ?? new List<ViewModelTurnstileLeadTypeSpecificSetting>()
			};
		}

		private AccountLeadTurnstileSettings GetAccountLeadTurnstileSettingsDataModel(ViewModelAccountLeadTurnstileSettings viewModel) {
			return new AccountLeadTurnstileSettings {
				IsTurnstileEnabled = viewModel.IsTurnstileEnabled,
				SiteKey = viewModel.SiteKey,
				SecretKey = viewModel.SecretKey,
				FailureMessage = viewModel.FailureMessage,
				FailureAction = viewModel.FailureAction,
				LeadTypeSettings = viewModel.LeadTypeSettings?.Select(x => new TurnstileLeadTypeSpecificSetting {
					LeadType = x.LeadType,
					IsActive = x.IsActive
				}).ToList() ?? new List<TurnstileLeadTypeSpecificSetting>()
			};
		}

		#endregion
	}
}
