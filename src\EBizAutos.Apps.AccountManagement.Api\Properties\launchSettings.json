{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iis": {"applicationUrl": "http://apps.dev.ebizautos/api/accounts", "sslPort": 0}, "iisExpress": {"applicationUrl": "http://localhost:51523", "sslPort": 0}}, "profiles": {"EbizAutos.Apps.AccountManagement.Api": {"commandName": "IIS", "launchBrowser": true, "launchUrl": "http://apps.dev.ebizautos/api/accounts/", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "http://localhost:5000"}, "IIS Express": {"commandName": "IIS", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "EbizAutos.Apps.AccountManagement.ApiV2": {"commandName": "Project", "launchBrowser": true, "launchUrl": "http://localhost:5013", "environmentVariables": {"ASPNETCORE_URLS": "http://localhost:5013", "ASPNETCORE_ENVIRONMENT": "Development"}}, "AccountManagementApiConsole": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "http://localhost:9003"}}}