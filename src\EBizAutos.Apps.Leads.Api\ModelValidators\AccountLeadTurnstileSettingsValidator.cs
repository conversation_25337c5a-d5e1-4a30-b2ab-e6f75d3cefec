using EBizAutos.Apps.Leads.Api.Models.AccountLeadSettings;
using FluentValidation;

namespace EBizAutos.Apps.Leads.Api.ModelValidators {
    public class AccountLeadTurnstileSettingsValidator : AbstractValidator<ViewModelAccountLeadTurnstileSettings> {
        public AccountLeadTurnstileSettingsValidator() {
            When(x => x.IsTurnstileEnabled, () => {
                RuleFor(x => x.SiteKey)
                    .NotEmpty()
                    .WithMessage("Site Key is required when Turnstile is enabled");
                
                RuleFor(x => x.SecretKey)
                    .NotEmpty()
                    .WithMessage("Secret Key is required when Turnstile is enabled");
            });

            RuleFor(x => x.FailureMessage)
                .MaximumLength(500)
                .WithMessage("Failure Message cannot exceed 500 characters");

            RuleFor(x => x.AccountId)
                .GreaterThan(0)
                .WithMessage("Account ID must be greater than 0");
        }
    }
}
