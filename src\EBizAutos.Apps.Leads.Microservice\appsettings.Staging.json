{"AppSettings": {"DataAccessEncryptKey": "fpABRddPOOg0hbm1PUHIjw==", "DataAccessEncryptIV": "AAAAAAAAAAAAAAAAAAAAAA==", "LegacyDataAccessEncryptKey": "7kkRcaz5l2TvAQzGxfKLF4a7oFlsrn6W", "LegacyDataAccessEncryptIV": "7rmR5t39n2sd", "HasToSendContactsProblemNotificationsToSlack": false}, "AppsWebAppConfiguration": {"LeadsCommunicationManageUrlTemplate": "http://apps.sandbox.ebizautos.com/leads/{0}/campaign/{1}/{2}"}, "AppsApiSettings": {"AppsBaseUrl": "http://sandbox-gateway.internal.aws.ebizautos.com/", "RequestTimeoutInMs": 30000, "AppsAuthorizationToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VyU2Vzc2lvbklkIjoiOTFjMzM5ZTktZDE0MC00MjZlLWFkNzAtMjEzNjY3MWFhNzQ3IiwiVXNlcklkIjoiNWIwMjhmZTUwNjhlOTExYTU4NmRlYzdlIiwiaXNzIjoiQXBwc0FwaVNlcnZlciIsImF1ZCI6IkFwcHNBcGlBdWRpZW5jZSJ9.GH7CArvyJsqZOH8wP3BZQhBQ7xIOa48HUjPheDQoq50"}, "DbSettings": {"AppsAccountsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/accounts?retryWrites=true&w=majority", "AppsSitesMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/sites?retryWrites=true&w=majority", "AppsLeadsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsleads?retryWrites=true&w=majority", "AppsLeadsMsSqlRepositoryConnectionString": "server=sandbox-sql.internal.aws.ebizautos.com; database=AppsLeads; User ID=sa; Pwd=********; Max Pool Size=300; Connection Timeout=30; Application Name=AppsLeads;", "AppsLeadsLogsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsleadslogs?retryWrites=true&w=majority", "EBizAutosMsSqlRepositoryConnectionString": "server=sandbox-sql.internal.aws.ebizautos.com; database=EBizAutos; User ID=sa; Pwd=********; Max Pool Size=300; Connection Timeout=30; Application Name=AppsLeads;", "AppsLeadsSearchDataMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsleadssearchdata?retryWrites=true&w=majority", "GalleryDataModificationTasksConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsgallerysynchronizationdata?retryWrites=true&w=majority", "VehicleMsSqlRepositoryTryCount": 3, "VehicleMsSqlRepositoryMinRetryDelayInMs": 500, "VehicleMsSqlRepositoryMaxRetryDelayInMs": 1000}, "NotificationSettings": {"DealerSocketApiUrl": "https://oemwebsecure.dealersocket.com/DSOEMLead/US/DCP/adf/1/SalesLead", "DealerSocketUserName": "802LFP6899", "DealerSocketPassword": "644MJZ9392", "EmailsSubstitution": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com"], "EmailValidationPattern": "^[_a-zA-Z0-9-]+(\\.[_a-zA-Z0-9-]+)*@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.(([0-9]{1,3})|([a-zA-Z]{2,3})|(aero|coop|info|museum|name))$", "MailServer": "email-01.internal.aws.ebizautos.com", "MailPort": 25, "MailUserName": "", "MailPassword": "", "MailEnableSsl": false, "EmailFrom": "<EMAIL>", "ErrorEmailsTo": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com"], "IsLoggingOn": true, "UnitedShippingWebServiceUrl": "https://new.vehicletransportusa.com/api/penske/quote", "UnitedShippingAuthorizationHeader": "BP49sRBwhkng8uVSY3YA5wTa"}, "EmailProxySettings": {"ForwardBoxPop3Server": "sandbox-email-proxy-01.internal.aws.ebizautos.com", "ForwardBoxUserName": "<EMAIL>", "ForwardBoxUserPassword": "eBiz89148#", "ForwardBoxDomain": "ebizautos.tv", "IsSecureSertificateValidationTurnedOff": true, "MailServer": "email-01.internal.aws.ebizautos.com", "MailPort": 25, "MailUserName": "", "MailPassword": "", "MailEnableSsl": false, "EmailFromForRedirectedEmails": "<EMAIL>", "MaxEmailsToReadCount": 10}, "TwilioSettings": {"AccountSid": "**********************************", "AuthToken": "2c8d79bc082dc1ae81107e556d4745cb", "HasToAuthenticateWebhooks": false, "DealerApplicationSid": "APe14aeed1876c1cca5a44f6917419f830", "CustomerApplicationSid": "AP0c1834da1166a7737e1404d21c51f0e6", "ApiUrl": "https://api.twilio.com", "HasToRecordCall": true, "MessageStatusCallbackUrl": "https://xhfgpk08dc.execute-api.us-east-1.amazonaws.com/api/leads/twilio/webhook/message/callback/", "RecordingStatusCallbackUrl": "https://xhfgpk08dc.execute-api.us-east-1.amazonaws.com/api/leads/twilio/webhook/call/recording/callback/", "DefaultDisclaimerText": "Thank you for calling! To enjoy the highest level of customer care this call may be recorded.\tPlease hold while we connect your call.", "LookupPricing": {"CallerNamePrice": 0.01}, "DownloadSettings": {"InitialDirectory": "\\\\sandbox-storage.internal.aws.ebizautos.com\\apps-sandbox-storage-disk-01\\assets.sandbox.ebizautos.com\\twilio\\", "UserName": "ebizdev", "Password": "hwG3doN*$mP0"}, "DeleteSettings": {"MaxStorageTimeInMonths": 6}, "ApiErrorCodesForRetry": [21422], "AudioRecordsHost": "https://assets.sandbox.ebizautos.com/twilio", "GetPricingCallTimeoutInMs": 1000, "CountOfTriesToGetPricing": 5, "UserProxyPhoneBuyTimeoutInMs": 60000, "UserProxyPhoneBuyLockRecheckTimeoutInMs": 2000, "MaxAllowedDistanceInMilesForFindingNumber": 150, "MessagingServiceFilter": "eBizAutos Leads Campaigns"}, "IntegrityReportSettings": {"ProcessingStartTime": "01:00", "ProcessingAttemptsCount": 3, "ProcessingDelayOnRetry": "00:01", "IsLoggingOn": true}, "UtilitySettings": {"CloseAccountDelayInDays": 1, "EmptyCommumicationContactsErrorEmailsTo": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com"], "ArchivedConversationDetailsAccountsReadBatchSize": 100, "ArchivedConversationDetailsReadBatchIntervalInMs": 200, "ArchivedConversationDetailsExecutionIntervalInHr": 6, "ArchivedConversationDetailsSelfCheckIntervalInMs": 1000, "ArchivedConversationDetailsFailedExecutionDelayInMs": 5000, "ConversationDetailsArchivingAccountsReadBatchSize": 50, "ConversationDetailsArchivingReadBatchSize": 50, "ConversationDetailsArchivingReadBatchIntervalInMs": 200, "ConversationDetailsArchivingExecutionIntervalInHr": 6, "ConversationDetailsArchivingSelfCheckIntervalInMs": 1000, "ConversationDetailsArchivingFailedExecutionDelayInMs": 5000}, "ServiceBusSettings": {"Host": "b-14ab2686-6644-4584-b08a-5a4d86f5fad1-1.mq.us-east-1.amazonaws.com", "Port": 61617, "Username": "ebizeventbus", "Password": "z$32s0d-nd8G62!js83aPuhpUh", "FailoverHosts": ["b-14ab2686-6644-4584-b08a-5a4d86f5fad1-1.mq.us-east-1.amazonaws.com"], "HasToUseSsl": true, "IsLoggingOn": true, "LogsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsservicebuslogs?retryWrites=true&w=majority", "ReceiveEndpointSettings": {"EBayReceiveEndpointSettings": {"EBayEventsEndpointEnabled": false, "EBayEventsEndpointConsumersCount": 1, "EBayEventsEndpointRetries": 1, "EBayEventsEndpointRetriesIntervalInSec": 300, "IsEBayAuctionOfferCreatedEventRedeliveryEnabled": true, "EBayAuctionOfferCreatedEventRedeliveryCount": 3, "EBayAuctionOfferCreatedEventRedeliveryTimeoutInMin": 30, "IsEBayNotificationEventRedeliveryEnabled": true, "EBayNotificationEventRedeliveryCount": 3, "EBayNotificationEventRedeliveryTimeoutInMin": 30}, "AccountReceiveEnpointSettings": {"IsAccountEventsEndpointEnabled": false, "AccountEventsEndpointConsumersCount": 1, "AccountEventsEndpointRetries": 1, "AccountEventsEndpointRetriesIntervalInSec": 300, "IsAccountEventsEnpointRedeliveryEnabled": true, "AccountEventsEnpointRedeliveryCount": 2, "AccountEventsEnpointRedeliveryTimeoutInMinutes": 30}, "WebFormLeadReceiveEnpointSettings": {"IsEventsEndpointEnabled": false, "EventsEndpointConsumersCount": 1}}, "PublishSettings": {"RetryAttempts": 3, "MinRetryDelayInMs": 5000, "MaxRetryDelayInMs": 10000}}, "ExceptionSettings": {"ErrorWebServiceLocation": "http://errors.internal.aws.ebizautos.com/queuedservice.asmx", "ApplicationCategoryId": 1, "MailServer": "email-01.internal.aws.ebizautos.com", "MailPort": 25, "MailUserName": "", "MailPassword": "", "MailEnableSsl": false, "ErrorEmailFrom": "<EMAIL>", "ErrorEmailTo": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com"], "EmailCc": [], "HasToUseServiceBus": true}, "HubsSettings": {"SMSHubSettings": {"HashingHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 1000, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 70000, "TasksLoadingActivationBound": 0, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 50, "FailedSpinDelayInMs": 1000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 200, "NoTaskDelayMaxInMs": 500, "MaxProcessingAttemptsCount": 3}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:02:00", "TaskSuspendingExpirationTime": "00:10:00", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "VoiceHubSettings": {"HashingHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 1000, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 70000, "TasksLoadingActivationBound": 0, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 50, "FailedSpinDelayInMs": 1000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 200, "NoTaskDelayMaxInMs": 500, "MaxProcessingAttemptsCount": 3}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:02:00", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "StatisticHubSettings": {"HashingHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 1000, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 70000, "TasksLoadingActivationBound": 0, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 100, "FailedSpinDelayInMs": 2000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 50, "NoTaskDelayMaxInMs": 100, "MaxProcessingAttemptsCount": 3}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:30:00", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "UtilityHubSettings": {"HashingHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 1000, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 70000, "TasksLoadingActivationBound": 0, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 50, "FailedSpinDelayInMs": 1000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 200, "NoTaskDelayMaxInMs": 500, "MaxProcessingAttemptsCount": 3}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:02:00", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "EmailHubSettings": {"HashingHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 1000, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 70000, "TasksLoadingActivationBound": 0, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 50, "FailedSpinDelayInMs": 1000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 200, "NoTaskDelayMaxInMs": 500, "MaxProcessingAttemptsCount": 3}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:02:00", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "EmailForwardHubSettings": {"TaskCheckFruequencyInMs": 100, "CountOfTasksPerCheck": 10, "CountOfTriesToProcessTask": 3, "TimeIntervalToReprocessFailedTasksInMin": 30, "OnErrorThreadSleepingTimeInSec": 60, "OnSuccessThreadSleepingTimeInSec": 30}, "FailedTasksHubSettings": {"TaskCheckFruequencyInMs": 60000, "CountOfTasksPerCheck": 10, "CountOfTriesToProcessTask": 3, "TimeIntervalToReprocessFailedTasksInMin": 30, "DelayBeforeSendingFirstMessageTimeInSec": 1, "OnErrorThreadSleepingTimeInSec": 60, "ActorAssumeFreezedTimeoutInSec": 900}, "SynchronizationHubSettings": {"HashingHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 1000, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 70000, "TasksLoadingActivationBound": 0, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 50, "FailedSpinDelayInMs": 1000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 200, "NoTaskDelayMaxInMs": 500, "MaxProcessingAttemptsCount": 50}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:02:00", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "SynchronizationLowPriorityHubSettings": {"HashingHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 1000, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 70000, "TasksLoadingActivationBound": 0, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 50, "FailedSpinDelayInMs": 1000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 200, "NoTaskDelayMaxInMs": 500, "MaxProcessingAttemptsCount": 50}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:02:00", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "WebFormHubSettings": {"WebFormHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 1000, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 70000, "TasksLoadingActivationBound": 3, "TasksLoadingBatchSize": 10}, "WebFormProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 20, "FailedSpinDelayInMs": 1000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 50, "NoTaskDelayMaxInMs": 100}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:02:00", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "NotificationHubSettings": {"HubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 1000, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 70000, "TasksLoadingActivationBound": 0, "TasksLoadingBatchSize": 10}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 20, "FailedSpinDelayInMs": 1000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 50, "NoTaskDelayMaxInMs": 100, "MaxProcessingAttemptsCount": 20}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:02:00", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "SynchronizationWithLegacyHubSettings": {"HashingHubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 1000, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 70000, "TasksLoadingActivationBound": 0, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 50, "FailedSpinDelayInMs": 1000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 50, "NoTaskDelayMaxInMs": 100, "MaxProcessingAttemptsCount": 3}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:01:00", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 1000, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}, "EBayHubSettings": {"HubActorConfiguration": {"TaskLoaderEmptyResponseSpinDelayInMs": 5, "TaskLoaderFailSpinDelayInMs": 15000, "TaskLoaderResponseTimeoutInMs": 180000, "TasksLoadingActivationBound": 0, "TasksLoadingBatchSize": 10, "TaskProcessingResponseTimeoutInMs": 300000}, "ProcessorActorConfiguration": {"StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 5, "FailedSpinDelayInMs": 1000, "TaskResponseTimeoutInMs": 60000, "NoTaskDelayMinInMs": 5, "NoTaskDelayMaxInMs": 10, "MaxProcessingAttemptsCount": 10}, "RecoveryActorConfiguration": {"TaskProcessingExpirationTime": "00:00:55", "StartupDelayInMs": 1000, "SuccessSpinDelayInMs": 10, "FailedSpinDelayInMs": 5000, "AssumedTaskResponseTimeoutInMs": 60000}}}, "WebFormProcessingSettings": {"NumberOfAttemptsToProcess": 5}, "SlackApiConfiguration": {"EmailFrom": "<EMAIL>", "EmailsTo": ["<EMAIL>"]}}