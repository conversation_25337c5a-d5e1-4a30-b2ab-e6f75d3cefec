# Cloudflare Turnstile API Documentation

## Overview

This document describes the API endpoints for managing Cloudflare Turnstile settings for lead accounts. Turnstile settings are managed separately from other lead settings and can be configured by both administrators and dealers.

## Key Features

- **Separate Management**: Turnstile settings are managed on a separate tab, not with other lead settings
- **Role-based Access**: Different permissions for administrators and dealers
- **Preservation**: When turnstile node comes empty, existing database values are not overwritten
- **Lead Type Specific**: Settings can be configured per lead type

## API Endpoints

### Admin Endpoints

#### Get Turnstile Settings for Admin
```
GET /admin/accounts/{accountId}/turnstile-settings
```

**Authorization**: `LeadsManageCommunications` permission required

**Response**: `ViewModelAccountLeadTurnstileSettingsAdmin`
```json
{
  "accountId": 123,
  "isTurnstileEnabled": true,
  "siteKey": "0x4AAAAAAABkMYinukE_q1SI",
  "secretKey": "0x4AAAAAAABkMYinutE_q1SJ",
  "failureMessage": "Turnstile validation failed. Please try again.",
  "failureAction": 0,
  "leadTypeSettings": [
    {
      "leadType": 1,
      "isActive": true
    }
  ]
}
```

#### Update Turnstile Settings for Admin
```
POST /admin/accounts/{accountId}/turnstile-settings
```

**Authorization**: `LeadsManageCommunications` permission required

**Request Body**: `ViewModelAccountLeadTurnstileSettingsAdmin`

**Response**: `boolean` (success indicator)

### Dealer Endpoints

#### Get Turnstile Settings for Dealer
```
GET /dealer/accounts/{accountId}/turnstile-settings
```

**Authorization**: `LeadsManageDealerSettings` permission required

**Response**: `ViewModelAccountLeadTurnstileSettingsDealer`
```json
{
  "accountId": 123,
  "isTurnstileEnabled": true,
  "failureMessage": "Turnstile validation failed. Please try again.",
  "failureAction": 0,
  "leadTypeSettings": [
    {
      "leadType": 1,
      "isActive": true
    }
  ]
}
```

**Note**: Dealer model does not include `siteKey` and `secretKey` for security reasons.

#### Update Turnstile Settings for Dealer
```
POST /dealer/accounts/{accountId}/turnstile-settings
```

**Authorization**: `LeadsManageDealerSettings` permission required

**Request Body**: `ViewModelAccountLeadTurnstileSettingsDealer`

**Response**: `boolean` (success indicator)

## Data Models

### TurnstileFailureActionEnum
```csharp
public enum TurnstileFailureActionEnum {
    RejectOnFormSubmission = 0,
    AcceptAndGoToSpam = 1
}
```

### ViewModelTurnstileLeadTypeSpecificSetting
```csharp
public class ViewModelTurnstileLeadTypeSpecificSetting {
    public ApplicationEnums.Leads.LeadTypeEnum LeadType { get; set; }
    public bool IsActive { get; set; }
}
```

### Lead Types
Common lead types that can be configured:
- `Email = 1`
- `TradeAppraisal = 9`
- `CreaditApp = 10`
- `VehicleFinder = 13`
- `ScheduleAppointmentRequest = 14`
- `ContactUs = 40`
- `TextRequest = 45`

## Validation Rules

### Admin Settings
- `SiteKey` is required when `IsTurnstileEnabled` is true
- `SecretKey` is required when `IsTurnstileEnabled` is true
- `FailureMessage` cannot exceed 500 characters
- `AccountId` must be greater than 0

### Dealer Settings
- `FailureMessage` cannot exceed 500 characters
- `AccountId` must be greater than 0

## Important Notes

1. **Preservation of Settings**: When updating other account lead settings, existing Turnstile settings are preserved and not overwritten.

2. **Security**: Dealers cannot access or modify `SiteKey` and `SecretKey` values - these are admin-only fields.

3. **Default Values**: When Turnstile settings don't exist, default values are:
   - `IsTurnstileEnabled`: false
   - `FailureAction`: RejectOnFormSubmission
   - `FailureMessage`: "Turnstile validation failed. Please try again."

4. **Separate Management**: Turnstile settings are managed independently from other lead settings to allow for separate UI tabs and prevent accidental overwrites.

## Error Handling

All endpoints return standard API error responses:
- `400 Bad Request`: Invalid model or account ID
- `403 Forbidden`: Insufficient permissions
- `500 Internal Server Error`: Server-side errors

## Usage Examples

### Enable Turnstile for Email Leads (Admin)
```json
POST /admin/accounts/123/turnstile-settings
{
  "accountId": 123,
  "isTurnstileEnabled": true,
  "siteKey": "0x4AAAAAAABkMYinukE_q1SI",
  "secretKey": "0x4AAAAAAABkMYinutE_q1SJ",
  "failureMessage": "Please complete the security check.",
  "failureAction": 0,
  "leadTypeSettings": [
    {
      "leadType": 1,
      "isActive": true
    }
  ]
}
```

### Update Failure Message (Dealer)
```json
POST /dealer/accounts/123/turnstile-settings
{
  "accountId": 123,
  "isTurnstileEnabled": true,
  "failureMessage": "Security verification required. Please try again.",
  "failureAction": 1,
  "leadTypeSettings": [
    {
      "leadType": 1,
      "isActive": true
    }
  ]
}
```
