﻿@using EBizAutos.Apps.CommonLib.Utilities.Extensions
@using EBizAutos.Apps.CommonLib.Utilities.Helpers
@model EBizAutos.Apps.Leads.Api.Models.CreditApplication.ViewModelCreditApplication

@if (Model.IsVehiclePurchaseTrade) {
	@if (Model.VehicleOfInterest != null) {
		<!--- VEHICLE OF INTEREST --->
		<tr>
			<td style="font-size:13pt;font-weight:bold;padding-bottom:10px;"><br/>Vehicle of Interest<br/></td><td></td>
		</tr>
		<tr>
			<td colspan="2" style="border-top:1px solid black;font-size:11pt;padding-top:5px;">
				<span style="font-weight:bold;">Year/Make/Model </span>@Model.VehicleOfInterest.VehicleDescription
			</td>
		</tr>
		<tr>
			<td style="padding-top:5px;">
				<span style="font-weight:bold;">Stock #: </span>@Model.VehicleOfInterest.Stock
			</td>
			<td>
				<span style="font-weight:bold;">VIN: </span>@Model.VehicleOfInterest.Vin
			</td>
		</tr>
		<tr>
			<td style="padding-top:5px;">
				@if (!Model.VehicleOfInterest.Status.IsEmpty()) {
					<span style="font-weight:bold;">Condition: </span>
					@Model.VehicleOfInterest.Status
				}
			</td>
			<td>
				<span style="font-weight:bold;">Price: </span>@Model.VehicleOfInterest.VehiclePrice
			</td>
		</tr>
	}
	<!--- PURCHASE AND TRADE-IN --->
	<tr>
		<td style="font-size:13pt;font-weight:bold;padding-bottom:10px;"><br/>Purchase & Trade Information<br/></td><td></td>
	</tr>
	<tr>
		<td style="border-top:1px solid black;font-size:11pt;padding-top:5px;">
			@if (Model.Product != null) {
				<span style="font-weight:bold;">Dealership Name: </span>
				@Model.Product.Dealership
			}
		</td>
		<td style="border-top:1px solid black;">
			<span style="font-weight:bold;">Salesperson Name: </span>
			@Model.Product?.SalesPerson
		</td>
	</tr>
	<tr>
		<td style="padding-top:5px;">
			@if (Model.Product != null) {
				<span style="font-weight:bold;">Loan or Lease: </span>
				@Model.Product.ContractTypeLabel
			}
		</td>
		<td>
			@if (Model.Product != null) {
				<span style="font-weight:bold;">Lease Miles per Year: </span>
				@Model.Product.LeaseMiles
			}
		</td>
	</tr>
	<tr>
		<td colspan="2" style="padding-top:5px;">
			@if (Model.Product != null && Model.Product.CashDownSpecified) {
				<span style="font-weight:bold;">Cash Down Payment: </span>
				@Model.Product.CashDown[0].Value
			}
		</td>
	</tr>
	@if (Model.TradeVehicle != null) {
		<!--- trade-in vehicle exists--->
		<tr>
			<td colspan="2" style="padding-top:20px;">
				<span style="text-decoration:underline;font-weight:normal;">Trade-In Information</span>
			</td>
		</tr>
		<tr>
			<td style="font-size:11pt;padding-top:5px;">
				@if (Model.TradeVehicle.Year.IsSpecified() || Model.TradeVehicle.Make.IsSpecified() || Model.TradeVehicle.Model.IsSpecified()) {
					<span style="font-weight:bold;">Year/Make/Model: </span>
					@StringHelper.Join(" ", Model.TradeVehicle.Year, Model.TradeVehicle.Make, Model.TradeVehicle.Model)
				}
			</td>
			<td>
				@if (!Model.TradeVehicle.Vin.IsEmpty()) {
					<span style="font-weight:bold;">VIN: </span>
					@Model.TradeVehicle.Vin
				}
			</td>
		</tr>
		<tr>
			<td style="padding-top:5px;">
				@if (!Model.TradeVehicle.EstValue.IsEmpty()) {
					<span style="font-weight:bold;">Est. Trade-In Value: </span>
					@Model.TradeVehicle.EstValue
				}
			</td>
			<td style="padding-top:5px;">
				@if (!Model.TradeVehicle.Mileage.IsEmpty()) {
					<span style="font-weight:bold;">Mileage: </span>
					@Model.TradeVehicle.Mileage
				}
			</td>
		</tr>
	}
}
