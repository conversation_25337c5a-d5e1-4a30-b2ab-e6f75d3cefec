using System;
using EBizAutos.Apps.Authentication.CommonLib.Utilities.Attributes;
using EBizAutos.Apps.CommonLib.Enums;
using EBizAutos.Apps.Leads.Api.Managers;
using EBizAutos.Apps.Leads.Api.Models.AccountLeadSettings;
using EBizAutos.Apps.Leads.CommonLib.Models;
using EBizAutos.Apps.Leads.CommonLib.Models.Logs;
using EBizAutos.CommonLibCore;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;

namespace EBizAutos.Apps.Leads.Api.Controllers.Api {
	[Route("")]
	public sealed class AccountLeadSettingsController : BaseApiController {
		private readonly AccountLeadSettingsManager _leadSettingsManager = null;

		public AccountLeadSettingsController(IServiceProvider serviceProvider) : base(serviceProvider) {
			_leadSettingsManager = serviceProvider.GetRequiredService<AccountLeadSettingsManager>();
		}

		#region Get
		/// <summary>
		/// Get Account Lead Settings for Admin
		/// </summary>
		/// <param name="accountId">Account Id</param>
		/// <response code="200">Account Lead Settings</response>
		[UserAuthorize(AuthenticationEnums.UserPermissionEnum.LeadsManageCommunications, true)]
		[HttpGet("admin/accounts/{accountId:int}/settings")]
		[ProducesResponseType(typeof(ViewModelAccountLeadSettingsAdmin), StatusCodes.Status200OK)]
		public IActionResult GetLeadAccountSettingsForAdmin(int accountId) {
			if (!IsUserRequestAllowed(accountId)) {
				return Forbid();
			}

			if (accountId <= 0) {
				return BadRequest();
			}

			PromiseResult<ViewModelAccountLeadSettingsAdmin> settingsPromise = _leadSettingsManager.GetAdminSettingsViewModel(accountId);

			return ApiResult(settingsPromise);
		}
		
		/// <summary>
		/// Get Account Lead Settings for Dealer
		/// </summary>
		/// <param name="accountId">Account Id</param>
		/// <response code="200">Account Lead Settings</response>
		[UserAuthorize(AuthenticationEnums.UserPermissionEnum.LeadsManageDealerSettings, true)]
		[HttpGet("dealer/accounts/{accountId:int}/settings")]
		[ProducesResponseType(typeof(ViewModelAccountLeadSettingsDealer), StatusCodes.Status200OK)]
		public IActionResult GetLeadAccountSettingsForDealer(int accountId) {
			if (!IsUserRequestAllowed(accountId)) {
				return Forbid();
			}

			if (accountId <= 0) {
				return BadRequest();
			}

			PromiseResult<ViewModelAccountLeadSettingsDealer> settingsPromise = _leadSettingsManager.GetDealerSettingsViewModel(accountId);

			return ApiResult(settingsPromise);
		}

		/// <summary>
		/// Get Account Lead Turnstile Settings
		/// </summary>
		/// <param name="accountId">Account Id</param>
		/// <response code="200">Account Lead Turnstile Settings</response>
		[UserAuthorize(AuthenticationEnums.UserPermissionEnum.LeadsManageDealerSettings, true)]
		[HttpGet("accounts/{accountId:int}/turnstile-settings")]
		[ProducesResponseType(typeof(ViewModelAccountLeadTurnstileSettings), StatusCodes.Status200OK)]
		public IActionResult GetLeadAccountTurnstileSettings(int accountId) {
			if (!IsUserRequestAllowed(accountId)) {
				return Forbid();
			}

			if (accountId <= 0) {
				return BadRequest();
			}

			PromiseResult<ViewModelAccountLeadTurnstileSettings> settingsPromise = _leadSettingsManager.GetTurnstileSettingsViewModel(accountId);

			return ApiResult(settingsPromise);
		}
		#endregion

		#region Update
		/// <summary>
		/// Update Account Lead Settings for Admin
		/// </summary>
		/// <param name="accountId">Account Id</param>
		/// <param name="leadSettings">Lead Settings update model for Admin</param>
		[UserAuthorize(AuthenticationEnums.UserPermissionEnum.LeadsManageCommunications, true)]
		[HttpPost("admin/accounts/{accountId:int}/settings")]
		[ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
		public IActionResult UpdateAccountLeadSettingsForAdmin(int accountId, [FromBody] ViewModelAccountLeadSettingsAdmin leadSettings) {
			if (!IsUserRequestAllowed(accountId)) {
				return Forbid();
			}

			if (!ModelState.IsValid) {
				return InvalidModelApiResult();
			}

			if (leadSettings.AccountId != accountId) {
				return BadRequest($"{nameof(accountId)} is invalid");
			}

			StartLogging(accountId, UserLogItem.ActionTypeEnum.UpdateAccountLeadSettings);

			PromiseResult<AccountLeadSettings> updateSettingsPromise = _leadSettingsManager.UpdateAccountLeadSettings(
				accountId,
				leadSettings,
				UserLogRepository.GetLoggerGuid(),
				out AccountLeadSettings originalSettings
			);

			if (updateSettingsPromise.IsCompleted) {
				UserLogRepository.LogAccountLeadSettingsUpdate(originalSettings, updateSettingsPromise.Result);
			}
			
			EndLogging(updateSettingsPromise.AsPromiseResult<bool>(x => x != null));

			return ApiResult(updateSettingsPromise.AsPromiseResult(x => true));
		}
		
		/// <summary>
		/// Update Account Lead Settings for Dealer
		/// </summary>
		/// <param name="accountId">Account Id</param>
		/// <param name="leadSettings">Lead Settings update model for Dealer</param>
		[UserAuthorize(AuthenticationEnums.UserPermissionEnum.LeadsManageDealerSettings, true)]
		[HttpPost("dealer/accounts/{accountId:int}/settings")]
		[ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
		public IActionResult UpdateAccountLeadSettingsForDealer(int accountId, [FromBody] ViewModelAccountLeadSettingsDealer leadSettings) {
			if (!IsUserRequestAllowed(accountId)) {
				return Forbid();
			}

			if (!ModelState.IsValid) {
				return InvalidModelApiResult();
			}

			if (leadSettings.AccountId != accountId) {
				return BadRequest($"{nameof(accountId)} is invalid");
			}

			StartLogging(accountId, UserLogItem.ActionTypeEnum.UpdateAccountLeadSettings);

			PromiseResult<AccountLeadSettings> updateSettingsPromise = _leadSettingsManager.UpdateAccountLeadSettings(
				accountId,
				leadSettings,
				UserLogRepository.GetLoggerGuid(),
				out AccountLeadSettings originalSettings
			);

			if (updateSettingsPromise.IsCompleted) {
				UserLogRepository.LogAccountLeadSettingsUpdate(originalSettings, updateSettingsPromise.Result);
			}

			EndLogging(updateSettingsPromise.AsPromiseResult<bool>(x => x != null));

			return ApiResult(updateSettingsPromise.AsPromiseResult(x => true));
		}

		/// <summary>
		/// Update Account Lead Turnstile Settings
		/// </summary>
		/// <param name="accountId">Account Id</param>
		/// <param name="turnstileSettings">Turnstile Settings update model</param>
		[UserAuthorize(AuthenticationEnums.UserPermissionEnum.LeadsManageDealerSettings, true)]
		[HttpPost("accounts/{accountId:int}/turnstile-settings")]
		[ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
		public IActionResult UpdateAccountLeadTurnstileSettings(int accountId, [FromBody] ViewModelAccountLeadTurnstileSettings turnstileSettings) {
			if (!IsUserRequestAllowed(accountId)) {
				return Forbid();
			}

			if (!ModelState.IsValid) {
				return InvalidModelApiResult();
			}

			if (turnstileSettings.AccountId != accountId) {
				return BadRequest($"{nameof(accountId)} is invalid");
			}

			StartLogging(accountId, UserLogItem.ActionTypeEnum.UpdateAccountLeadSettings);

			PromiseResult<AccountLeadSettings> updateSettingsPromise = _leadSettingsManager.UpdateAccountLeadTurnstileSettings(
				accountId,
				turnstileSettings,
				UserLogRepository.GetLoggerGuid(),
				out AccountLeadSettings originalSettings
			);

			if (updateSettingsPromise.IsCompleted) {
				UserLogRepository.LogAccountLeadSettingsUpdate(originalSettings, updateSettingsPromise.Result);
			}

			EndLogging(updateSettingsPromise.AsPromiseResult<bool>(x => x != null));

			return ApiResult(updateSettingsPromise.AsPromiseResult(x => true));
		}
		#endregion
	}
}
