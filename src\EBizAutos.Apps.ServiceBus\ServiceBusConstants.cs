﻿namespace EBizAutos.Apps.ServiceBus {
	public static class ServiceBusConstants {
		public static class QueueNames {
			public static class EBay {
				public const string VehicleEvents = "ebiz_apps_ebay_vehicle_events";
				public const string ContactEvents = "ebiz_apps_ebay_contact_events";
				public const string OffersProcessedEvents = "ebiz_apps_ebay_offers_processed_events";
				public const string AuctionLegacySynchronizationEvents = "ebiz_apps_ebay_auction_ended_events";
				public const string AccountEvents = "ebiz_apps_ebay_account_events";
				public const string AuctionProcessingEvents = "ebiz_apps_ebay_auction_processing_events";
				public const string LegacyContactEvents = "ebiz_apps_ebay_legacy_contact_events";
				public const string LegacyAccountEvents = "ebiz_apps_ebay_legacy_account_events";
				public const string LegacyAuctionEvents = "ebiz_apps_ebay_legacy_auction_events";
				public const string NotificationCommands = "ebiz_apps_ebay_notification_commands";
				public const string VehicleSynchronizationProcessingMessages = "ebiz_apps_ebay_vehicle_synchronization_messages";
			}

			public static class InventoryManagement {
				public const string IntegrityReportMessages = "ebiz_apps_inventory_integrity_report_messages";
				public const string LagacySynchronizationMessages = "ebiz_apps_inventory_legacy_synchronization_messages";
			}

			public static class GoogleAnalytics {
				public const string AccountEvents = "ebiz_apps_analyticsapi_account_events";
				public const string GroupAccountRemovedEvents = "ebiz_apps_analyticsapi_groupaccountremoved_events";
				public const string GroupAccountDeletedEvents = "ebiz_apps_analyticsapi_accountgroupdeleted_events";
				public const string LeadsInsertedEvents = "ebiz_apps_analyticsapi_leadinserted_events";
				public const string TestEvents = "ebiz_apps_analyticsapi_test_events";
			}

			public static class Leads {
				public const string WebFormCommands = "ebiz_apps_leads_webform_commands";
				public const string GalleryLeadFormsTestEvents = "ebiz_gallery_leadforms_webapp_test_events";
			}

			public static class InventoryManagementVideoEncoder {
				public const string VehicleEvents = "ebiz_apps_inventory_video_encoder_vehicle_events";
				public const string RegenerateAutoVideoCommands = "ebiz_apps_inventory_video_encoder_regenerate_auto_video_commands";
				public const string TestErrorEvents = "ebiz_apps_inventory_video_encoder_test_events";
			}
		}
	}
}
