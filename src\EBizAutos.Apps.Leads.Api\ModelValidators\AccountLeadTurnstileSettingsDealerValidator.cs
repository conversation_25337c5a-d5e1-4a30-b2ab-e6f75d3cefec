using EBizAutos.Apps.Leads.Api.Models.AccountLeadSettings;
using FluentValidation;

namespace EBizAutos.Apps.Leads.Api.ModelValidators {
    public class AccountLeadTurnstileSettingsDealerValidator : AbstractValidator<ViewModelAccountLeadTurnstileSettingsDealer> {
        public AccountLeadTurnstileSettingsDealerValidator() {
            RuleFor(x => x.FailureMessage)
                .MaximumLength(500)
                .WithMessage("Failure Message cannot exceed 500 characters");

            RuleFor(x => x.AccountId)
                .GreaterThan(0)
                .WithMessage("Account ID must be greater than 0");
        }
    }
}
