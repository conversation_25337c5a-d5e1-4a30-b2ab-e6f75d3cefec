﻿using System;
using System.Linq;
using Castle.Core.Internal;
using EBizAutos.Apps.CommonLib.Utilities.Extensions;
using EBizAutos.Apps.Leads.CommonLib.Enums;
using EBizAutos.Apps.Leads.CommonLib.Models.CreditApplication;
using EBizAutos.Apps.Leads.CommonLib.Utilities.Helpers;
using EBizAutos.CommonLib.Extensions;

namespace EBizAutos.Apps.Leads.Api.Models.CreditApplication {
	public class ViewModelCreditApplication {
		#region Private
		private readonly CommonLib.Models.CreditApplication.CreditApplication _creditApplication = null;
		#endregion

		public string SourceCompanyName { get; set; }
		public int SourceAccountId { get; set; }
		public string CompanyName { get; set; }
		public DateTime CreatedDateTime { get; set; }

		public string LogoImageBase64 { get; set; }
		
		#region Credit Application XML
		public string RequestDate { get; }

		public ApplicationVehicle VehicleOfInterest { get; set; }

		public ApplicationVehicle TradeVehicle { get; private set; }

		public ApplicationProduct Product { get; private set; }

		public ApplicationApplicant PrimaryApplicant { get; private set; }
		public ApplicationApplicant CoApplicant { get; private set; }
		public ApplicationBusinessInfo Business { get; private set; }

		public ApplicationApplicantEmployer PrimaryApplicantCurrentEmployer { get; private set; }

		public ApplicationApplicantEmployer PrimaryApplicantPreviousEmployer { get; private set; }
		public bool IsPrimaryApplicantEmploymentSpecified { get; private set; }

		public ApplicationApplicantEmployer CoApplicantCurrentEmployer { get; private set; }
		public ApplicationApplicantEmployer CoApplicantPreviousEmployer { get; private set; }
		public bool IsCoApplicantEmploymentSpecified { get; private set; }

		public ApplicationApplicant Relative { get; private set; }
		public ApplicationApplicant Friend { get; private set; }
		public ApplicationApplicant Other { get; private set; }
		public bool IsOtherApplicantsSpecified { get; private set; }
		public string Comments { get; private set; }
		public string Signature { get; private set; }

		#region Finalization
		public bool IsVehiclePurchaseTrade { get; private set; }
		#endregion
		#endregion

		public ViewModelCreditApplication(CommonLib.Models.CreditApplication.CreditApplication creditApplication) {
			_creditApplication = creditApplication;

			RequestDate = CreditApplicationHelper.TransformDate(creditApplication.RequestDate);
			FillVehicleOfInterest();
			FillTradeInformation();
			FillProductInformation();
			FillApplicantInformation();
			FillCoApplicantInformation();
			FillBusinessInformation();
			FillPrimaryApplicantEmploymentInformation();
			FillCoApplicantEmploymentInformation();
			FillOtherInformation();

			FinalizeFill();
		}

		private void FinalizeFill() {
			IsVehiclePurchaseTrade = VehicleOfInterest != null || Product != null || TradeVehicle != null;
		}

		#region Builder
		private void FillVehicleOfInterest() {
			if (_creditApplication.Vehicle.Count < 1) {
				return;
			}

			ApplicationVehicle vehicleOfInterest = _creditApplication.Vehicle[0];
			if (vehicleOfInterest != null && (
				!vehicleOfInterest.Stock.IsEmpty() ||
				!vehicleOfInterest.Vin.IsEmpty() ||
				!vehicleOfInterest.Status.IsEmpty() ||
				!vehicleOfInterest.VehiclePrice.IsEmpty())) {
				VehicleOfInterest = vehicleOfInterest;
			}
		}

		private void FillTradeInformation() {
			if (!_creditApplication.VehicleSpecified) {
				return;
			}

			ApplicationVehicle tradeVehicle = _creditApplication.Vehicle[1];
			if (tradeVehicle != null && (
				!tradeVehicle.Vin.IsEmpty() ||
				!tradeVehicle.Year.IsEmpty() ||
				!tradeVehicle.Make.IsEmpty() ||
				!tradeVehicle.Model.IsEmpty() ||
				!tradeVehicle.Mileage.IsEmpty() ||
				!tradeVehicle.EstValue.IsEmpty())) {
				TradeVehicle = tradeVehicle;
			}
		}

		private void FillProductInformation() {
			if (_creditApplication.Product.Count == 0) {
				return;
			}

			ApplicationProduct product = _creditApplication.Product[0];
			if (product != null && (
				!product.Dealership.IsEmpty() ||
				!product.SalesPerson.IsEmpty() ||
				product.ContractType != CreditApplicationEnums.ContractTypeEnum.Undefined ||
				!product.LeaseMiles.IsEmpty() ||
				(product.CashDownSpecified &&
				!product.CashDown[0].Value.IsEmpty()))) {
				Product = product;
			}
		}

		private void FillApplicantInformation() {
			ApplicationApplicant primaryApplicant = _creditApplication.Applicant.FirstOrDefault(x => x.ApplicantType == CreditApplicationEnums.ApplicantTypeEnum.Primary);
			if (primaryApplicant != null && (
				!primaryApplicant.TaxId.IsEmpty() ||
				!primaryApplicant.DriverLicence.IsEmpty() ||
				!primaryApplicant.Dob.IsEmpty() ||
				!primaryApplicant.MaritalStatus.IsEmpty())) {
				PrimaryApplicant = primaryApplicant;
			}
		}

		private void FillOtherInformation() {
			ApplicationApplicant applicant = _creditApplication.Applicant.FirstOrDefault(x => x.ApplicantType == CreditApplicationEnums.ApplicantTypeEnum.Relative);
			if (IsOtherApplicantInfoSpecified(applicant)) {
				Relative = applicant;
				IsOtherApplicantsSpecified = true;
			}

			applicant = _creditApplication.Applicant.FirstOrDefault(x => x.ApplicantType == CreditApplicationEnums.ApplicantTypeEnum.Friend);
			if (IsOtherApplicantInfoSpecified(applicant)) {
				Friend = applicant;
				IsOtherApplicantsSpecified = true;
			}

			applicant = _creditApplication.Applicant.FirstOrDefault(x => x.ApplicantType == CreditApplicationEnums.ApplicantTypeEnum.Other);
			if (IsOtherApplicantInfoSpecified(applicant)) {
				Other = applicant;
				IsOtherApplicantsSpecified = true;
			}
			
			ApplicationApplicant primaryApplicant = _creditApplication.Applicant.FirstOrDefault(x => x.ApplicantType == CreditApplicationEnums.ApplicantTypeEnum.Primary);
			if (primaryApplicant != null) {
				Comments = primaryApplicant.Comments.GetElementOrNullAt(0)?.Value;
				Signature = primaryApplicant.Signature;
			}
		}

		private void FillCoApplicantInformation() {
			ApplicationApplicant coApplicant = _creditApplication.Applicant.FirstOrDefault(x => x.ApplicantType == CreditApplicationEnums.ApplicantTypeEnum.CoApplicant);
			if (coApplicant != null && (
				!coApplicant.TaxId.IsEmpty() ||
				!coApplicant.DriverLicence.IsEmpty() ||
				!coApplicant.Dob.IsEmpty() ||
				!coApplicant.MaritalStatus.IsEmpty()
			)) {
				CoApplicant = coApplicant;
			}
		}

		private void FillBusinessInformation() {
			ApplicationBusinessInfo business = _creditApplication.BusinessInfo.FirstOrDefault();
			if (business != null && (
				!business.BusName.IsEmpty() ||
				(business.BusTypeSpecified &&
				!business.BusType[0].Type.IsEmpty()) ||
				!business.BusTaxId.IsEmpty() ||
				!business.Description.IsEmpty() ||
				business.Phone.Any(x => x.PhoneType == CreditApplicationEnums.PhoneTypeEnum.Voice || x.PhoneType == CreditApplicationEnums.PhoneTypeEnum.Fax) ||
				business.Duration.Any(x => x.DurationType == CreditApplicationEnums.DurationTypeEnum.Months || x.DurationType == CreditApplicationEnums.DurationTypeEnum.Years) ||
				(business.AddressSpecified &&
				business.Address[0].AddresslineSpecified &&
				(
					!business.Address[0].Addressline[0].IsEmpty() ||
					business.Address[0].Addressline.Count > 1 && !business.Address[0].Addressline[1].IsEmpty() ||
					!business.Address[0].CityStateZip.IsEmpty()
				)))) {
				Business = business;
			}
		}

		private void FillPrimaryApplicantEmploymentInformation() {
			ApplicationApplicantEmployer employer = PrimaryApplicant?.Employer?.FirstOrDefault(x => x.EmployerStatus == CreditApplicationEnums.EmployerStatusEnum.Current);
			if (IsEmployerSpecified(employer)) {
				PrimaryApplicantCurrentEmployer = employer;
				IsPrimaryApplicantEmploymentSpecified = true;
			}

			employer = PrimaryApplicant?.Employer?.FirstOrDefault(x => x.EmployerStatus == CreditApplicationEnums.EmployerStatusEnum.Previous);
			if (IsEmployerSpecified(employer)) {
				PrimaryApplicantPreviousEmployer = employer;
				IsPrimaryApplicantEmploymentSpecified = true;
			}
		}

		private void FillCoApplicantEmploymentInformation() {
			ApplicationApplicantEmployer employer = CoApplicant?.Employer?.FirstOrDefault(x => x.EmployerStatus == CreditApplicationEnums.EmployerStatusEnum.Current);
			if (IsEmployerSpecified(employer)) {
				CoApplicantCurrentEmployer = employer;
				IsCoApplicantEmploymentSpecified = true;
			}

			employer = CoApplicant?.Employer?.FirstOrDefault(x => x.EmployerStatus == CreditApplicationEnums.EmployerStatusEnum.Previous);
			if (IsEmployerSpecified(employer)) {
				CoApplicantPreviousEmployer = employer;
				IsCoApplicantEmploymentSpecified = true;
			}
		}
		#endregion

		#region Helpers
		private static bool IsEmployerSpecified(ApplicationApplicantEmployer employer) {
			return employer != null && (
				employer.Name.IsNullOrEmpty() ||
				employer.Self.IsSpecified() ||
				employer.Position.IsSpecified() ||
				employer.Duration.Any(x => x.DurationType == CreditApplicationEnums.DurationTypeEnum.Months || x.DurationType == CreditApplicationEnums.DurationTypeEnum.Years) ||
				employer.SalarySpecified);
		}

		private static bool IsOtherApplicantInfoSpecified(ApplicationApplicant applicant) {
			return applicant != null && (
				applicant.FullName.IsSpecified() ||
				applicant.PhoneSpecified ||
				(applicant.AddressSpecified &&
				(applicant.Address[0].AddresslineSpecified ||
				applicant.Address[0].CityStateZip.IsSpecified())));
		}
		#endregion
	}
}
