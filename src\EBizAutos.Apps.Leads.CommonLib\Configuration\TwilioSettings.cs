namespace EBizAutos.Apps.Leads.CommonLib.Configuration {
	public class TwilioSettings {
		public class TwilioLookupPricing {
			public double CallerNamePrice { get; set; }
		}

		public class TwilioDownloadSettings {
			public string InitialDirectory { get; set; }
			public string UserName { get; set; }
			public string Password { get; set; }
		}

		public class TwilioDeleteSettings {
			public int? MaxStorageTimeInMonths { get; set; } // to prevent removing all folders when config is broken
		}
		public int AmountOfTriesToBuyPhoneNumber { get; set; }
		public int RetryDelayInMs { get; set; }
		public int[] ApiErrorCodesForRetry { get; set; } = new int[] { };
		public string AccountSid { get; set; }
		public string AuthToken { get; set; }
		public bool HasToAuthenticateWebhooks { get; set; }
		public string DealerApplicationSid { get; set; }
		public string CustomerApplicationSid { get; set; }
		public string ApiUrl { get; set; }
		public string MessageStatusCallbackUrl { get; set; }
		public string RecordingStatusCallbackUrl { get; set; }
		public double CallerNameLookupPrice { get; set; }
		public TwilioLookupPricing LookupPricing { get; set; }
		public TwilioDownloadSettings DownloadSettings { get; set; }
		public TwilioDeleteSettings DeleteSettings { get; set; }
		public string AudioRecordsHost { get; set; }
		public int GetPricingCallTimeoutInMs { get; set; }
		public int CountOfTriesToGetPricing { get; set; }
		public int UserProxyPhoneBuyTimeoutInMs { get; set; } = 60000; // 60 seconds
		public int UserProxyPhoneBuyLockRecheckTimeoutInMs { get; set; } = 2000; // 2 seconds
		public int MaxCountOfTriesToLockUserPhone { get; set; } = 3;
		public int UserPhoneTryLockTimeoutInMs { get; set; } = 1000; // 1 seconds
		public int MaxAllowedDistanceInMilesForFindingNumber { get; set; } = 100;
		public string MessagingServiceFilter { get; set; } // Filter text for Twilio messaging services
	}
}
