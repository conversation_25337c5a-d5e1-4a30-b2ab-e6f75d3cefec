{"Kestrel": {"EndPoints": {"Http": {"Url": "http://0.0.0.0:9013"}}}, "AppSettings": {"ApplicationName": "Apps Leads API", "IsDev": "false", "DataAccessEncryptKey": "fpABRddPOOg0hbm1PUHIjw==", "DataAccessEncryptIV": "AAAAAAAAAAAAAAAAAAAAAA==", "LegacyDataAccessEncryptKey": "7kkRcaz5l2TvAQzGxfKLF4a7oFlsrn6W", "LegacyDataAccessEncryptIV": "7rmR5t39n2sd", "MaxDailyRange": "30", "MaxDailyOffset": "90", "LogParallelismDegree": 0, "IsLoggingOn": "true", "IsCreatingEmailCampaignBlocked": false, "HasToSendContactsProblemNotificationsToSlack": true}, "AppsWebAppConfiguration": {"LeadsCommunicationManageUrlTemplate": "https://apps.ebizautos.com/leads/{0}/campaign/{1}/{2}", "AccountLeadsSettingsManageUrlTemplate": "https://apps.ebizautos.com/leads/{0}/settings"}, "AppsApiSettings": {"AppsBaseUrl": "http://apps-web-03.internal.aws.ebizautos.com/", "RequestTimeoutInMs": 30000, "AppsAuthorizationToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VyU2Vzc2lvbklkIjoiZTQyYmQwMmYtM2EzMi00NTE1LWFmNzYtMzVkMWZmNTNmOWVlIiwiVXNlcklkIjoiNWIwM2QxOWQ4N2M5M2IxYzMwN2FhZThmIiwiaXNzIjoiQXBwc0FwaVNlcnZlciIsImF1ZCI6IkFwcHNBcGlBdWRpZW5jZSJ9.nNYUb3rCK3ejJLn2C_qxSg3gwnB79jnQgs3RceR7dIo"}, "ExceptionSettings": {"ErrorWebServiceLocation": "http://errors.internal.aws.ebizautos.com/queuedservice.asmx", "ApplicationCategoryId": 1, "MailServer": "email-01.internal.aws.ebizautos.com", "MailPort": 25, "MailUserName": "", "MailPassword": "", "MailEnableSsl": false, "ErrorEmailFrom": "<EMAIL>", "ErrorEmailTo": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com", "<EMAIL>", "<EMAIL>"], "EmailCc": [], "HasToUseServiceBus": true}, "DbSettings": {"UsersMongoDbRepositoryConnectionStrings": "mongodb+srv://ebizdev:<EMAIL>/cp?retryWrites=true&w=majority", "UsersMongoDbSecondaryRepositoryConnectionStrings": "mongodb+srv://ebizdev:<EMAIL>/cp?retryWrites=true&w=majority&readPreference=secondaryPreferred", "AppsLeadsStatisticMongoDbRepositoryConnectionStrings": "mongodb+srv://ebizdev:<EMAIL>/appsleads?retryWrites=true&w=majority&readPreference=secondaryPreferred", "AppsLeadsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsleads?retryWrites=true&w=majority", "AppsLeadsMongoDbSecondaryRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsleads?retryWrites=true&w=majority&readPreference=secondaryPreferred", "AppsLeadsLogsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsleadslogs?retryWrites=true&w=majority&readPreference=secondaryPreferred", "AppsLeadsMsSqlRepositoryConnectionString": "server=data01.internal.aws.ebizautos.com; database=AppsLeads; User ID=sa; Pwd=********; Max Pool Size=300; Connection Timeout=30; Application Name=appsleadswebapp;", "SettingsMongoDbRepositoryConnectionStrings": "mongodb+srv://ebizdev:<EMAIL>/cp?retryWrites=true&w=majority", "AppsLeadsSearchDataMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsleadssearchdata?retryWrites=true&w=majority", "AppsLeadsSearchDataMongoDbSecondaryRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsleadssearchdata?retryWrites=true&w=majority&readPreference=secondaryPreferred", "GalleryDataModificationTasksConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsgallerysynchronizationdata?retryWrites=true&w=majority", "AppsAccountsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/accounts?retryWrites=true&w=majority", "AppsAccountsMongoDbSecondaryRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/accounts?retryWrites=true&w=majority&readPreference=secondaryPreferred", "AppsServiceBusLogsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsservicebuslogs?retryWrites=true&w=majority&readPreference=secondaryPreferred", "AppsContactsReportFiltersMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsleads?retryWrites=true&w=majority"}, "TwilioSettings": {"AccountSid": "**********************************", "AuthToken": "2c8d79bc082dc1ae81107e556d4745cb", "HasToAuthenticateWebhooks": true, "DealerApplicationSid": "AP8afd249ea0732bdb411a0767b4d27da5", "CustomerApplicationSid": "AP356ca1cfe7701e1ad2591f1e989b9763", "ApiUrl": "https://api.twilio.com", "HasToRecordCall": true, "MessageStatusCallbackUrl": "https://apps.ebizautos.com/api/leads/twilio/webhook/message/callback/", "RecordingStatusCallbackUrl": "https://apps.ebizautos.com/api/leads/twilio/webhook/call/recording/callback/", "DefaultDisclaimerText": "Thank you for calling, to enjoy the highest level of customer care this call may be recorded, please hold while we connect your call", "LookupPricing": {"CallerNamePrice": 0.01}, "AudioRecordsHost": "https://assets.ebizautos.media/twilio", "AmountOfTriesToBuyPhoneNumber": 3, "RetryDelayInMs": 1, "ApiErrorCodesForRetry": [21422], "UserProxyPhoneBuyTimeoutInMs": 60000, "UserProxyPhoneBuyLockRecheckTimeoutInMs": 2000, "MaxCountOfTriesToLockUserPhone": 5, "UserPhoneTryLockTimeoutInMs": 1000, "MaxAllowedDistanceInMilesForFindingNumber": 150, "MessagingServiceFilter": "eBizAutos Leads Campaigns"}, "NotificationSettings": {"DealerSocketApiUrl": "https://oemwebsecure.dealersocket.com/DSOEMLead/US/DCP/adf/1/SalesLead", "DealerSocketUserName": "802LFP6899", "DealerSocketPassword": "644MJZ9392", "EmailsSubstitution": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com", "<EMAIL>", "<EMAIL>"], "EmailValidationPattern": "^[_a-zA-Z0-9-]+(\\.[_a-zA-Z0-9-]+)*@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.(([0-9]{1,3})|([a-zA-Z]{2,3})|(aero|coop|info|museum|name))$", "MailServer": "email-01.internal.aws.ebizautos.com", "MailPort": 25, "MailUserName": "", "MailPassword": "", "MailEnableSsl": false, "EmailFrom": "<EMAIL>", "ErrorEmailsTo": ["alex.i<PERSON><PERSON><PERSON>@ebizautos.com", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "IsLoggingOn": true}, "EmailProxySettings": {"ForwardBoxDomain": "ebizdealers.com"}, "EBaySettings": {"AuctionUrlTemplate": "https://www.ebay.com/itm/{0}?mkrid=711-53200-19255-0&siteid=0&mkcid=1&campid=5335872257&toolid=10001&customid=&mkevt=1"}, "WebFormProcessingSettings": {"NumberOfAttemptsToProcess": 5}, "ServiceBusSettings": {"Host": "b-51d9b697-1aa7-4d44-9987-4c73fcc0ce0e-1.mq.us-east-1.amazonaws.com", "Port": 61617, "Username": "ebizeventbus", "Password": "z$32s0d-nd8G62!js83aPuhpUh", "FailoverHosts": ["b-51d9b697-1aa7-4d44-9987-4c73fcc0ce0e-1.mq.us-east-1.amazonaws.com", "b-51d9b697-1aa7-4d44-9987-4c73fcc0ce0e-2.mq.us-east-1.amazonaws.com"], "HasToUseSsl": true, "PublishSettings": {"RetryAttempts": 3, "MinRetryDelayInMs": 1000, "MaxRetryDelayInMs": 3000}, "LogsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsservicebuslogs?retryWrites=true&w=majority", "ReceiveEndpointSettings": {"ConversationDetailsSavedEventConsumersCount": 3}}, "SlackApiConfiguration": {"EmailFrom": "<EMAIL>", "EmailsTo": ["<EMAIL>"]}}