﻿using System;
using System.IO;
using EBizAutos.ApplicationCommonLib.Applications.Amazon.ParameterStore;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Serilog;

namespace EBizAutos.Apps.EBay.Api {
	public class Program {
		public static void Main(string[] args) {
			CreateWebHostBuilder(args).Build().Run();
		}

		public static IWebHostBuilder CreateWebHostBuilder(string[] args) =>
			WebHost.CreateDefaultBuilder(args)
				.ConfigureAppConfiguration((hostContext, config) => {
						// delete all default configuration providers
						config.Sources.Clear();
						IHostingEnvironment env = hostContext.HostingEnvironment;
						string applicationName = env.IsDevelopment() ? AppDomain.CurrentDomain.FriendlyName : new DirectoryInfo(env.ContentRootPath).Name;

						config.AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: false)
							.AddJsonFile($"appsettings.{env.EnvironmentName}.serilog.json", optional: false)
							.AddAwsConfigurationParameters(applicationName)
							.AddEnvironmentVariables();
					}
				)
				.UseStartup<Startup>()
				.UseSerilog((context, config) => {
						config.ReadFrom.Configuration(context.Configuration);
					}
				)
				.UseKestrel((builderContext, options) => {
						options.Configure(builderContext.Configuration.GetSection("Kestrel"));
					}
				);
	}
}
