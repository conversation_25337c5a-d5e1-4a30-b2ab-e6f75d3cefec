﻿namespace EBizAutos.Apps.GoogleAnalytics.BrandExport.MongoDbRepository {
	public sealed class GoogleAnalyticsUniversalCollectionNames : IGoogleAnalyticsCollectionNames {
		private GoogleAnalyticsUniversalCollectionNames() {
		}

		public static GoogleAnalyticsUniversalCollectionNames Instance => new GoogleAnalyticsUniversalCollectionNames();

		public string DesktopOverviewReportsCollection => "DesktopOverviewReportsCollection";
		public string DesktopEngagementReportsCollection => "DesktopEngagementReportsCollection";
		public string MobileOverviewReportsCollection => "MobileOverviewReportsCollection";
		public string MobileEngagementReportsCollection => "MobileEngagementReportsCollection";
		public string WebsiteDigitalAdvertisingReportsCollection => "WebsiteDigitalAdvertisingReportsCollection";
		public string MobileDigitalAdvertisingReportsCollection => "MobileDigitalAdvertisingReportsCollection";
	}
}