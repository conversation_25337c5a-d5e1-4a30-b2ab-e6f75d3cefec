﻿using EBizAutos.Apps.InventoryManagement.VideoEncoderService.ServiceBus.Consumers.Events.Vehicle;
using EBizAutos.Apps.ServiceBus;
using EBizAutos.Apps.ServiceBus.Commands.VideoEncoder;
using EBizAutos.Apps.ServiceBus.Configuration;
using EBizAutos.Apps.ServiceBus.Events.Tests;
using EBizAutos.Apps.ServiceBus.Events.Vehicle;
using EBizAutos.Apps.ServiceBus.Extensions;
using EBizAutos.Apps.ServiceBus.Interfaces;
using EBizAutos.CommonLib.ServiceBus;
using GreenPipes.Partitioning;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace EBizAutos.Apps.InventoryManagement.VideoEncoderService.DI {
	public static class ServiceBusCollectionExtensions {
		internal static void RegisterServiceBus(this IServiceCollection serviceCollection, AppConfiguration appConfiguration, IHostingEnvironment hostingEnvironment) {
			serviceCollection.Scan(
				scan => scan
					.FromAssemblyOf<VehicleCreatedEventConsumer>()
					.AddClasses(classes => classes.AssignableTo(typeof(IConsumer<>))).AsImplementedInterfaces().WithTransientLifetime()
			);

			ServiceBusSettings serviceBusSettings = appConfiguration.ServiceBusSettings;

			serviceCollection.AddLogDecoratedAppsServiceBus(
				new HostSettings(
					appConfiguration.ServiceBusSettings.Host,
					appConfiguration.ServiceBusSettings.Port,
					appConfiguration.ServiceBusSettings.Username,
					appConfiguration.ServiceBusSettings.Password,
					appConfiguration.ServiceBusSettings.FailoverHosts,
					appConfiguration.ServiceBusSettings.HasToUseSsl,
					appConfiguration.ServiceBusSettings.TransportOptions
				),
				new PublishSettings(
					serviceBusSettings.PublishSettings.RetryAttempts,
					serviceBusSettings.PublishSettings.MinRetryDelayInMs,
					serviceBusSettings.PublishSettings.MaxRetryDelayInMs
				),
				(serviceBusConfigurator, serviceProvider) => {
					ConfigureVehicleEventsReceiveEndpoint(serviceBusSettings.ReceiveEndpointSettings.VehicleEventsReceiveEndpointSettings, serviceBusConfigurator, serviceProvider, hostingEnvironment);
					ConfigureRegenerateAutoVideoReceiveEndpoint(serviceBusSettings.ReceiveEndpointSettings.RegenerateAutoVideoReceiveEndpointSettings, serviceBusConfigurator, serviceProvider, hostingEnvironment);
					ConfigureTestErrorEventsReceiveEndpoint(serviceBusConfigurator, serviceProvider);
				}
			);
		}

		private static void ConfigureVehicleEventsReceiveEndpoint(
			ReceiveEndpointSettings receiveEndpointSettings,
			IMasstransitServiceBusConfigurator serviceBusConfigurator,
			IServiceProvider serviceProvider,
			IHostingEnvironment hostingEnvironment) {
			if (!receiveEndpointSettings.IsEndpointEnabled ||
				receiveEndpointSettings.ConsumersCount <= 0) {
				return;
			}

			serviceBusConfigurator.ReceiveEndpoint(
				ServiceBusConstants.QueueNames.InventoryManagementVideoEncoder.VehicleEvents,
				configurator => {
					configurator.AutoDelete = !hostingEnvironment.IsProduction(); // auto delete on sbox and dev
					configurator.Durable = true;
					configurator.UseConcurrencyLimit(receiveEndpointSettings.ConsumersCount);
					configurator.UseErrorMonitorToLogFaultedMessages();
					IPartitioner partitioner = configurator.CreatePartitioner(1);
					if (receiveEndpointSettings.IsRedeliveryEnabled &&
						receiveEndpointSettings.MaxRedeliveryCount > 0) {
						configurator.UseIntervalMessageRedelivery(
							receiveEndpointSettings.MaxRedeliveryCount,
							TimeSpan.FromMinutes(receiveEndpointSettings.RedeliveryIntervalInMin)
						);
					}

					if (receiveEndpointSettings.IsRetryEnabled &&
						receiveEndpointSettings.MaxRetriesCount > 0) {
						configurator.UseIntervalMessageRetry(
							receiveEndpointSettings.MaxRetriesCount,
							TimeSpan.FromSeconds(receiveEndpointSettings.RetriesIntervalInSec)
						);
					}

					configurator.Consumer(
						serviceProvider.GetRequiredService<IConsumer<IVehicleCreatedEvent>>,
						cfg => {
							cfg.UsePartitioner(partitioner, x => x.Iid);
						}
					);

					configurator.Consumer(
						serviceProvider.GetRequiredService<IConsumer<IVehicleUpdatedEvent>>,
						cfg => {
							cfg.UsePartitioner(partitioner, x => x.Iid);
						}
					);

					configurator.Consumer(
						serviceProvider.GetRequiredService<IConsumer<IVehicleDeletedEvent>>,
						cfg => {
							cfg.UsePartitioner(partitioner, x => x.Iid);
						}
					);
				}
			);
		}

		private static void ConfigureRegenerateAutoVideoReceiveEndpoint(
			ReceiveEndpointSettings regenerateAutoVideoReceiveEndpointSettings,
			IMasstransitServiceBusConfigurator serviceBusConfigurator,
			IServiceProvider serviceProvider,
			IHostingEnvironment hostingEnvironment) {
			if (!regenerateAutoVideoReceiveEndpointSettings.IsEndpointEnabled ||
				regenerateAutoVideoReceiveEndpointSettings.ConsumersCount <= 0) {
				return;
			}

			serviceBusConfigurator.ReceiveEndpoint(
				ServiceBusConstants.QueueNames.InventoryManagementVideoEncoder.RegenerateAutoVideoCommands,
				configurator => {
					configurator.AutoDelete = !hostingEnvironment.IsProduction(); // auto delete on sbox and dev
					configurator.Durable = true;
					configurator.UseConcurrencyLimit(regenerateAutoVideoReceiveEndpointSettings.ConsumersCount);
					configurator.UseErrorMonitorToLogFaultedMessages();
					IPartitioner partitioner = configurator.CreatePartitioner(1);
					if (regenerateAutoVideoReceiveEndpointSettings.IsRedeliveryEnabled &&
						regenerateAutoVideoReceiveEndpointSettings.MaxRedeliveryCount > 0) {
						configurator.UseIntervalMessageRedelivery(
							regenerateAutoVideoReceiveEndpointSettings.MaxRedeliveryCount,
							TimeSpan.FromMinutes(regenerateAutoVideoReceiveEndpointSettings.RedeliveryIntervalInMin)
						);
					}

					if (regenerateAutoVideoReceiveEndpointSettings.IsRetryEnabled &&
						regenerateAutoVideoReceiveEndpointSettings.MaxRetriesCount > 0) {
						configurator.UseIntervalMessageRetry(
							regenerateAutoVideoReceiveEndpointSettings.MaxRetriesCount,
							TimeSpan.FromSeconds(regenerateAutoVideoReceiveEndpointSettings.RetriesIntervalInSec)
						);
					}

					configurator.Consumer(
						serviceProvider.GetRequiredService<IConsumer<IRegenerateAccountPhotoToVideoCommand>>,
						cfg => {
							cfg.UsePartitioner(partitioner, x => x.AccountId);
						}
					);

					configurator.Consumer(
						serviceProvider.GetRequiredService<IConsumer<IRegenerateVehiclePhotoToVideoCommand>>,
						cfg => {
							cfg.UsePartitioner(partitioner, x => x.AccountId);
						}
					);

					configurator.MapEndpoint<IRegenerateAccountPhotoToVideoCommand>();
					configurator.MapEndpoint<IRegenerateVehiclePhotoToVideoCommand>();
				}
			);
		}

		private static void ConfigureTestErrorEventsReceiveEndpoint(
			IMasstransitServiceBusConfigurator serviceBusConfigurator, 
			IServiceProvider serviceProvider) {
			serviceBusConfigurator.ReceiveEndpoint(
				ServiceBusConstants.QueueNames.InventoryManagementVideoEncoder.TestErrorEvents, 
				configurator => {
					configurator.Durable = true;
					configurator.Consumer(
						serviceProvider.GetRequiredService<IConsumer<ITestErrorEvent>>,
						cfg => {
							cfg.UseConcurrentMessageLimit(3);
						}
					);
				}
			);
		}
	}
}
