﻿using System;
using System.Xml;
using EBizAutos.ApplicationCommonLib.Applications.Enums;
using EBizAutos.Apps.CommonLib.Models.AppsAccountSettings.DealerInformation;
using EBizAutos.Apps.CommonLib.Models.AppsSiteSettings.Presentation;
using EBizAutos.Apps.Leads.CommonLib.Enums;
using EBizAutos.Apps.Leads.CommonLib.Models;
using EBizAutos.Apps.Leads.CommonLib.Models.Notification;
using EBizAutos.CommonLib.Extensions;

namespace EBizAutos.Apps.Leads.BusinessLib.Notification.XmlBuilders.EbizData {
	public class WebFormEBizDataXmlBuilder : BaseEBizDataXmlBuilder {
		private readonly ConversationWebFormDetails _webFormConversationDetails = null;
		private readonly AppsDealerInformation _vehicleOwnerDealerInformation = null;

		public WebFormEBizDataXmlBuilder(ConversationWebFormDetails conversationDetails, AppsDealerInformation vehicleOwnerDealerInformation) : base(conversationDetails) {
			_webFormConversationDetails = conversationDetails;
			_vehicleOwnerDealerInformation = vehicleOwnerDealerInformation;
		}

		public override string ConvertToXml(
			LeadsNotificationModel notificationModel,
			Communication communication,
			AppsDealerInformation dealerInformation,
			AppsSiteSettingsBasic siteSettings,
			AccountLeadSettings accountLeadSettings,
			LeadsSystemSettings leadsSystemSettings
			) {
			var leadType = _webFormConversationDetails.RequestInformation.LeadType;
			var xmlDocument = new XmlDocument();
			var xmlData = xmlDocument.AppendChild(xmlDocument.CreateElement("data"));

			#region requestinformation
			var xmlRequestInfo = AppendChildElement(xmlData, "requestinformation");

			AppendChildText(AppendChildElement(xmlRequestInfo, "accountid"), _webFormConversationDetails.RequestInformation.AccountId);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "sourceaccountid", _webFormConversationDetails.RequestInformation.SourceAccountId);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "itemaccountid", _webFormConversationDetails.RequestInformation.ItemAccountId);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "leadtypeid", (int) _webFormConversationDetails.RequestInformation.LeadType);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "leadsourceid", (int) _webFormConversationDetails.RequestInformation.LeadSource);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "contactid", _webFormConversationDetails.RequestInformation.ContactId);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "campaignid", _webFormConversationDetails.RequestInformation.CampaignId);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "segmentdefaultcampaignid", _webFormConversationDetails.RequestInformation.SegmentDefaultCampaignId);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "initialreferer", _webFormConversationDetails.RequestInformation.InitialReferer);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "initialtarget", _webFormConversationDetails.RequestInformation.InitialTarget);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "leadsourceip", _webFormConversationDetails.RequestInformation.LeadSourceIp);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "leadsourcepage", _webFormConversationDetails.RequestInformation.LeadSourcePage);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "gallerypageid", _webFormConversationDetails.RequestInformation.GalleryPageId);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "gallerypagetypeid", _webFormConversationDetails.RequestInformation.GalleryPageTypeId);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "usehostleadcontact", _webFormConversationDetails.RequestInformation.HasToUseHostLeadContact);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "dealerzipcode", dealerInformation.Address.Zip);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "dealerzipcodelabel", dealerInformation.Address.Country.ZipCodeLabel);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "dealercountry", dealerInformation.Address.Country.Name);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "sitedefaulturl", notificationModel.DealerContactInfo.Url);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "sitedefaulturlwithprotocol", siteSettings.UrlWithProtocol);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "requestdate", _webFormConversationDetails.RequestInformation.RequestDate.ToString("yyyy-MM-dd"));
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "requesttime", _webFormConversationDetails.RequestInformation.RequestDate.ToString("HH:mm:ss"));
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "moneysign", _webFormConversationDetails.RequestInformation.MoneySign);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "currentyear", DateTime.Now.Year);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "leadsourceoperatingsystem", _webFormConversationDetails.RequestInformation.LeadSourceOperatingSystem);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "leadsourcebrowser", _webFormConversationDetails.RequestInformation.LeadSourceBrowser);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "isout", _webFormConversationDetails.RequestInformation.IsOut);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "portaltypeid", _webFormConversationDetails.RequestInformation.PortalTypeId);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "ismobiledevice", _webFormConversationDetails.RequestInformation.IsMobileDevice);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "ccpaconsent", _webFormConversationDetails.RequestInformation.HasCCPAConsent.ToString().ToLower());
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "tcpaconsent", _webFormConversationDetails.RequestInformation.HasTCPAConsent.ToString().ToLower());
			
			if (!string.IsNullOrEmpty(accountLeadSettings.NotificationSettings.ShiftDigitalId) && accountLeadSettings.NotificationSettings.ShiftDigitalType == NotificationEnums.ShiftDigitalTypeEnum.Mini) {
				AppendChildWithFirstNonEmptyText(xmlRequestInfo, "tcpadisclaimer", string.Format(leadsSystemSettings.TCPADisclaimer, dealerInformation.CompanyName));
				AppendChildWithFirstNonEmptyText(xmlRequestInfo, "ccpadisclaimer", string.Format(leadsSystemSettings.CCPADisclaimer, dealerInformation.CompanyName));
			}
			
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "sessionid", _webFormConversationDetails.RequestInformation.SessionId);
			AppendChildWithFirstNonEmptyText(xmlRequestInfo, "leadid", _webFormConversationDetails.RequestInformation.LeadId);
			#endregion

			#region usercontactinformation
			var xmlUserContact = AppendChildElement(xmlData, "usercontactinformation");

			AppendChildWithFirstNonEmptyText(xmlUserContact, "firstname", _webFormConversationDetails.UserContactInformation.FirstName);
			AppendChildWithFirstNonEmptyText(xmlUserContact, "lastname", _webFormConversationDetails.UserContactInformation.LastName);
			AppendChildWithFirstNonEmptyText(xmlUserContact, "email", _webFormConversationDetails.UserContactInformation.Email);
			AppendChildWithFirstNonEmptyText(xmlUserContact, "phone", _webFormConversationDetails.UserContactInformation.Phone);
			AppendChildWithFirstNonEmptyText(xmlUserContact, "address1", _webFormConversationDetails.UserContactInformation.Address1);
			AppendChildWithFirstNonEmptyText(xmlUserContact, "address2", _webFormConversationDetails.UserContactInformation.Address2);
			AppendChildWithFirstNonEmptyText(xmlUserContact, "city", _webFormConversationDetails.UserContactInformation.City);
			AppendChildWithFirstNonEmptyText(xmlUserContact, "zipcode", _webFormConversationDetails.UserContactInformation.Zip);
			AppendChildWithFirstNonEmptyText(xmlUserContact, "state", _webFormConversationDetails.UserContactInformation.State);
			AppendChildWithFirstNonEmptyText(xmlUserContact, "language", _webFormConversationDetails.UserContactInformation.Language);
			AppendChildWithFirstNonEmptyText(xmlUserContact, "besttimetocontact", _webFormConversationDetails.UserContactInformation.BestTimeToContact);
			AppendChildWithFirstNonEmptyText(xmlUserContact, "timezone", _webFormConversationDetails.UserContactInformation.Timezone);
			AppendChildWithFirstNonEmptyText(xmlUserContact, "purchasetimeframe", _webFormConversationDetails.UserContactInformation.PurchaseTimeFrame);
			AppendChildWithFirstNonEmptyText(xmlUserContact, "preferredcontacttypeid", (byte) _webFormConversationDetails.RequestInformation.PreferredContactType);
			AppendChildWithFirstNonEmptyText(xmlUserContact, "comments", _webFormConversationDetails.RequestInformation.Comments);
			#endregion

			#region description
			var xmlDescription = AppendChildElement(xmlData, "description");
			if (leadType == ApplicationEnums.Leads.LeadTypeEnum.ScheduleAppointmentRequest) {
				AppendChildText(xmlDescription, _webFormConversationDetails.SelectedServices?.Description);
			} else if (leadType == ApplicationEnums.Leads.LeadTypeEnum.PartsRequest) {
				AppendChildText(xmlDescription, _webFormConversationDetails.Parts?.Description);
			} else {
				AppendChildText(xmlDescription, "");
			}
			#endregion

			#region preferenceinformation
			if (_webFormConversationDetails.PreferenceInformation != null) {
				var xmlPreferenceInfo = AppendChildElement(xmlData, "preferenceinformation");

				AppendChildWithFirstNonEmptyText(xmlPreferenceInfo, "firsttimepreferencedate", _webFormConversationDetails.PreferenceInformation.FirstTimePreferenceDate);
				AppendChildWithFirstNonEmptyText(xmlPreferenceInfo, "firsttimepreferencetime", _webFormConversationDetails.PreferenceInformation.FirstTimePreferenceTime);
				AppendChildWithFirstNonEmptyText(xmlPreferenceInfo, "secondtimepreferencedate", _webFormConversationDetails.PreferenceInformation.SecondTimePreferenceDate);
				AppendChildWithFirstNonEmptyText(xmlPreferenceInfo, "secondtimepreferencetime", _webFormConversationDetails.PreferenceInformation.SecondTimePreferenceTime);
			}
			#endregion

			#region vehicleinformation
			var xmlVehicleInfo = AppendChildElement(xmlData, "vehicleinformation");

			if (leadType == ApplicationEnums.Leads.LeadTypeEnum.TradeAppraisal && _webFormConversationDetails.TradeInformation != null) {
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "year", _webFormConversationDetails.TradeInformation.Year);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "make", _webFormConversationDetails.TradeInformation.Make);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "model", _webFormConversationDetails.TradeInformation.Model);

				if (!string.IsNullOrEmpty(_webFormConversationDetails.TradeInformation.Vin)) {
					AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "vin", _webFormConversationDetails.TradeInformation.Vin);
				} else if (_webFormConversationDetails.VehicleInformation != null &&
							!string.IsNullOrEmpty(_webFormConversationDetails.VehicleInformation.Vin) &&
							string.IsNullOrEmpty(_webFormConversationDetails.VehicleInformation.Iid?.ToString())) {
					AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "vin", _webFormConversationDetails.VehicleInformation.Vin);
				}

				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "mileage", _webFormConversationDetails.TradeInformation.Mileage);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "exteriorcolor", _webFormConversationDetails.TradeInformation.ExteriorColor);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "interiorcolor", _webFormConversationDetails.TradeInformation.InteriorColor);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "condition", _webFormConversationDetails.TradeInformation.Condition);
			} else if (_webFormConversationDetails.VehicleInformation != null) {
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "accountid", _webFormConversationDetails.VehicleInformation.AccountId);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "price", _webFormConversationDetails.VehicleInformation.Price);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "year", _webFormConversationDetails.VehicleInformation.Year);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "make", _webFormConversationDetails.VehicleInformation.Make);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "model", _webFormConversationDetails.VehicleInformation.Model);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "trim", _webFormConversationDetails.VehicleInformation.Trim);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "iid", _webFormConversationDetails.VehicleInformation.Iid);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "vin", _webFormConversationDetails.VehicleInformation.Vin);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "yearfrom", _webFormConversationDetails.VehicleInformation.YearFrom);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "yearto", _webFormConversationDetails.VehicleInformation.YearTo);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "bodystyle", _webFormConversationDetails.VehicleInformation.BodyStyle);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "transmission", _webFormConversationDetails.VehicleInformation.Transmission);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "milesfrom", _webFormConversationDetails.VehicleInformation.MilesFrom);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "milesto", _webFormConversationDetails.VehicleInformation.MilesTo);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "mileage", _webFormConversationDetails.VehicleInformation.Mileage);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "mileageunits", _webFormConversationDetails.VehicleInformation.MileageUnits);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "exteriorcolor", _webFormConversationDetails.VehicleInformation.ExteriorColor);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "interiorcolor", _webFormConversationDetails.VehicleInformation.InteriorColor);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "pricefrom", _webFormConversationDetails.VehicleInformation.PriceFrom);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "priceto", _webFormConversationDetails.VehicleInformation.PriceTo);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "condition", _webFormConversationDetails.VehicleInformation.Condition);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "iscpo", _webFormConversationDetails.VehicleInformation.IsCpo);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "stocknum", _webFormConversationDetails.VehicleInformation.StockNum);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "priceoffered", _webFormConversationDetails.VehicleInformation.PriceOffered);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "presentationphotopath", _webFormConversationDetails.VehicleInformation.PresentationPhotoPath);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "transmissiondescription", _webFormConversationDetails.VehicleInformation.TransmissionDescription);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "enginedescription", _webFormConversationDetails.VehicleInformation.EngineDescription);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "fueltype", _webFormConversationDetails.VehicleInformation.FuelType);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "promotext", _webFormConversationDetails.VehicleInformation.PromoText);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "contactid", _webFormConversationDetails.VehicleInformation.ContactId);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "auctionid", _webFormConversationDetails.VehicleInformation.AuctionId);
				AppendChildWithFirstNonEmptyText(xmlVehicleInfo, "inventorytypeid", _webFormConversationDetails.VehicleInformation.InventoryTypeId);
			}
			#endregion

			#region specials
			if (_webFormConversationDetails.Specials != null) {
				var xmlSpecials = AppendChildElement(xmlData, "specials");

				AppendChildWithFirstNonEmptyText(xmlSpecials, "specialid", _webFormConversationDetails.Specials.SpecialId);
				AppendChildWithFirstNonEmptyText(xmlSpecials, "specialtype", _webFormConversationDetails.Specials.SpecialType);
				AppendChildWithFirstNonEmptyText(xmlSpecials, "specialcategory", _webFormConversationDetails.Specials.SpecialCategory);
				AppendChildWithFirstNonEmptyText(xmlSpecials, "specialtitle", _webFormConversationDetails.Specials.SpecialTitle);
				AppendChildWithFirstNonEmptyText(xmlSpecials, "specialline1", _webFormConversationDetails.Specials.SpecialLine1);
				AppendChildWithFirstNonEmptyText(xmlSpecials, "specialline2", _webFormConversationDetails.Specials.SpecialLine2);

				if (!string.IsNullOrEmpty(_webFormConversationDetails.Specials.SpecialPhotoPath)) {
					string photoPath = _webFormConversationDetails.Specials.SpecialPhotoPath.StartsWith("http://") || _webFormConversationDetails.Specials.SpecialPhotoPath.StartsWith("https://")
						? _webFormConversationDetails.Specials.SpecialPhotoPath
						: "https:" + _webFormConversationDetails.Specials.SpecialPhotoPath;

					AppendChildText(AppendChildElement(xmlSpecials, "specialphotopath"), photoPath);
				}

				AppendChildWithFirstNonEmptyText(xmlSpecials, "specialinventorytype", _webFormConversationDetails.Specials.SpecialInventoryType);

				if (_webFormConversationDetails.Specials.SpecialInventoryMake != null) {
					var xmlSpecialsGroupedData = AppendChildElement(xmlSpecials, "grouppedinventorydata");

					AppendChildText(AppendChildElement(xmlSpecialsGroupedData, "year"), _webFormConversationDetails.Specials.SpecialInventoryYear);
					AppendChildText(AppendChildElement(xmlSpecialsGroupedData, "make"), _webFormConversationDetails.Specials.SpecialInventoryMake);
					AppendChildWithFirstNonEmptyText(xmlSpecialsGroupedData, "_notificationModel", _webFormConversationDetails.Specials.SpecialInventoryModel);
					AppendChildWithFirstNonEmptyText(xmlSpecialsGroupedData, "trim", _webFormConversationDetails.Specials.SpecialInventoryTrim);
					AppendChildWithFirstNonEmptyText(xmlSpecialsGroupedData, "stock", _webFormConversationDetails.Specials.SpecialInventoryStockNum);
					AppendChildWithFirstNonEmptyText(xmlSpecialsGroupedData, "vin", _webFormConversationDetails.Specials.SpecialInventoryVin);
				} else {
					AppendChildText(AppendChildElement(xmlSpecials, "year"), _webFormConversationDetails.Specials.SpecialInventoryYear);
				}
			}
			#endregion

			#region parts
			if (_webFormConversationDetails.Parts != null) {
				var xmlParts = AppendChildElement(xmlData, "parts");

				AppendChildText(AppendChildElement(xmlParts, "description"), _webFormConversationDetails.Parts.Description);
			}
			#endregion

			#region selectedservices
			if (_webFormConversationDetails.SelectedServices != null) {
				var xmlSelectedServices = AppendChildElement(xmlData, "selectedservices");

				AppendChildWithFirstNonEmptyText(xmlSelectedServices, "recommendedmaintenance", _webFormConversationDetails.SelectedServices.RecommendedMaintenance);
				AppendChildWithFirstNonEmptyText(xmlSelectedServices, "enginerepair", _webFormConversationDetails.SelectedServices.EngineRepair);
				AppendChildWithFirstNonEmptyText(xmlSelectedServices, "exhaustrepair", _webFormConversationDetails.SelectedServices.ExhaustRepair);
				AppendChildWithFirstNonEmptyText(xmlSelectedServices, "startingorbatteryissues", _webFormConversationDetails.SelectedServices.StartingOrBatteryIssues);
				AppendChildWithFirstNonEmptyText(xmlSelectedServices, "acorheatingissue", _webFormConversationDetails.SelectedServices.AcOrHeatingIssue);
				AppendChildWithFirstNonEmptyText(xmlSelectedServices, "alignment", _webFormConversationDetails.SelectedServices.Alignment);
				AppendChildWithFirstNonEmptyText(xmlSelectedServices, "transmissionissues", _webFormConversationDetails.SelectedServices.TransmissionIssues);
				AppendChildWithFirstNonEmptyText(xmlSelectedServices, "brakingsystem", _webFormConversationDetails.SelectedServices.BrakingSystem);
				AppendChildWithFirstNonEmptyText(xmlSelectedServices, "electricalandaudiosystem", _webFormConversationDetails.SelectedServices.ElectricalAndAudioSystem);
				AppendChildWithFirstNonEmptyText(xmlSelectedServices, "emissionsinspection", _webFormConversationDetails.SelectedServices.EmissionsInspection);
				AppendChildWithFirstNonEmptyText(xmlSelectedServices, "oilandfilterchange", _webFormConversationDetails.SelectedServices.OilAndFilterChange);
				AppendChildWithFirstNonEmptyText(xmlSelectedServices, "description", _webFormConversationDetails.SelectedServices.Description);
			}
			#endregion

			#region appointmentinformation
			if (_webFormConversationDetails.Appointment != null) {
				var xmlAppointment = AppendChildElement(xmlData, "appointmentinformation");

				AppendChildWithFirstNonEmptyText(xmlAppointment, "date", _webFormConversationDetails.Appointment.Date);
				AppendChildWithFirstNonEmptyText(xmlAppointment, "time", _webFormConversationDetails.Appointment.Time);
			}
			#endregion

			#region purchaseinformation
			if (_webFormConversationDetails.PurchaseInformation != null) {
				var xmlPruchaseInfo = AppendChildElement(xmlData, "purchaseinformation");

				if (leadType == ApplicationEnums.Leads.LeadTypeEnum.CreaditApp) {
					string dealershipname;
					if (_vehicleOwnerDealerInformation != null) {
						dealershipname = _vehicleOwnerDealerInformation.CompanyName;
					} else {
						dealershipname = dealerInformation.CompanyName;
					}
					AppendChildWithFirstNonEmptyText(xmlPruchaseInfo, "dealershipname", dealershipname);
				}

				AppendChildWithFirstNonEmptyText(xmlPruchaseInfo, "salespersonname", _webFormConversationDetails.PurchaseInformation.SalespersonName);
				AppendChildWithFirstNonEmptyText(xmlPruchaseInfo, "loanorlease", _webFormConversationDetails.PurchaseInformation.LoanOrLease);
				AppendChildWithFirstNonEmptyText(xmlPruchaseInfo, "leasemilesperyear", _webFormConversationDetails.PurchaseInformation.LeaseMilesPerYear);
				AppendChildWithFirstNonEmptyText(xmlPruchaseInfo, "cashdownpayment", _webFormConversationDetails.PurchaseInformation.CashDownPayment);
			}
			#endregion

			#region purchaseinformation
			if (_webFormConversationDetails.TradeInformation != null) {
				var xmlTradeInfo = AppendChildElement(xmlData, "tradeininformation");
				AppendChildWithFirstNonEmptyText(xmlTradeInfo, "vin", _webFormConversationDetails.TradeInformation.Vin);
				AppendChildWithFirstNonEmptyText(xmlTradeInfo, "year", _webFormConversationDetails.TradeInformation.Year);
				AppendChildWithFirstNonEmptyText(xmlTradeInfo, "make", _webFormConversationDetails.TradeInformation.Make);
				AppendChildWithFirstNonEmptyText(xmlTradeInfo, "model", _webFormConversationDetails.TradeInformation.Model);
				AppendChildWithFirstNonEmptyText(xmlTradeInfo, "_notificationModel", _webFormConversationDetails.TradeInformation.Model);
				AppendChildWithFirstNonEmptyText(xmlTradeInfo, "mileage", _webFormConversationDetails.TradeInformation.Mileage);
				AppendChildWithFirstNonEmptyText(xmlTradeInfo, "estimatedtradeinvalue", _webFormConversationDetails.TradeInformation.EstTradeInValue);
			}
			#endregion

			#region vehicleofinterest
			if (_webFormConversationDetails.VehicleOfInterest != null) {
				var xmlVehicleOfInterest = AppendChildElement(xmlData, "vehicleofinterest");

				AppendChildWithFirstNonEmptyText(xmlVehicleOfInterest, "iid", _webFormConversationDetails.VehicleOfInterest.Iid);
				AppendChildWithFirstNonEmptyText(xmlVehicleOfInterest, "year", _webFormConversationDetails.VehicleOfInterest.Year);
				AppendChildWithFirstNonEmptyText(xmlVehicleOfInterest, "make", _webFormConversationDetails.VehicleOfInterest.Make);
				AppendChildWithFirstNonEmptyText(xmlVehicleOfInterest, "_notificationModel", _webFormConversationDetails.VehicleOfInterest.Model);
				AppendChildWithFirstNonEmptyText(xmlVehicleOfInterest, "inventorytypeid", _webFormConversationDetails.VehicleOfInterest.InventoryType);
			}
			#endregion

			#region businessinformation
			if (_webFormConversationDetails.BusinessInformation != null) {
				var xmlBusinessInformation = AppendChildElement(xmlData, "businessinformation");

				AppendChildWithFirstNonEmptyText(xmlBusinessInformation, "name", _webFormConversationDetails.BusinessInformation.Name);
				AppendChildWithFirstNonEmptyText(xmlBusinessInformation, "taxid", _webFormConversationDetails.BusinessInformation.TaxID);
				AppendChildWithFirstNonEmptyText(xmlBusinessInformation, "phone", _webFormConversationDetails.BusinessInformation.Phone);
				AppendChildWithFirstNonEmptyText(xmlBusinessInformation, "fax", _webFormConversationDetails.BusinessInformation.Fax);
				AppendChildWithFirstNonEmptyText(xmlBusinessInformation, "type", _webFormConversationDetails.BusinessInformation.Type);
				AppendChildWithFirstNonEmptyText(xmlBusinessInformation, "description", _webFormConversationDetails.BusinessInformation.Desc);
				AppendChildWithFirstNonEmptyText(xmlBusinessInformation, "years", _webFormConversationDetails.BusinessInformation.AgeYears);
				AppendChildWithFirstNonEmptyText(xmlBusinessInformation, "month", _webFormConversationDetails.BusinessInformation.AgeMonth);

				if (_webFormConversationDetails.BusinessInformation.Address != null) {
					var xmlBusinessInformationAddress = AppendChildElement(xmlBusinessInformation, "address");

					AppendChildWithFirstNonEmptyText(xmlBusinessInformationAddress, "address1", _webFormConversationDetails.BusinessInformation.Address.Address1);
					AppendChildWithFirstNonEmptyText(xmlBusinessInformationAddress, "address2", _webFormConversationDetails.BusinessInformation.Address.Address2);
					AppendChildWithFirstNonEmptyText(xmlBusinessInformationAddress, "city", _webFormConversationDetails.BusinessInformation.Address.City);
					AppendChildWithFirstNonEmptyText(xmlBusinessInformationAddress, "state", _webFormConversationDetails.BusinessInformation.Address.State);
					AppendChildWithFirstNonEmptyText(xmlBusinessInformationAddress, "zip", _webFormConversationDetails.BusinessInformation.Address.Zip);
				}
			}
			#endregion

			#region applicantinformation
			if (_webFormConversationDetails.ApplicantInformation != null) {
				var xmlApplicantInformation = AppendChildElement(xmlData, "applicantinformation");

				AppendChildWithFirstNonEmptyText(xmlApplicantInformation, "firstname", _webFormConversationDetails.ApplicantInformation.FirstName);
				AppendChildWithFirstNonEmptyText(xmlApplicantInformation, "initial", _webFormConversationDetails.ApplicantInformation.Initial);
				AppendChildWithFirstNonEmptyText(xmlApplicantInformation, "lastname", _webFormConversationDetails.ApplicantInformation.LastName);
				AppendChildWithFirstNonEmptyText(xmlApplicantInformation, "suffix", _webFormConversationDetails.ApplicantInformation.Suffix);
				AppendChildWithFirstNonEmptyText(xmlApplicantInformation, "socsecnum", _webFormConversationDetails.ApplicantInformation.SocSecNum);

				if (_webFormConversationDetails.ApplicantInformation.DriverLicense != null) {
					var xmlDriverLicense = AppendChildElement(xmlApplicantInformation, "driverlicense");

					AppendChildWithFirstNonEmptyText(xmlDriverLicense, "licensenumber", _webFormConversationDetails.ApplicantInformation.DriverLicense.Num);
					AppendChildWithFirstNonEmptyText(xmlDriverLicense, "state", _webFormConversationDetails.ApplicantInformation.DriverLicense.State);
					AppendChildWithFirstNonEmptyText(xmlDriverLicense, "expirationdate", _webFormConversationDetails.ApplicantInformation.DriverLicense.Exp);
				}

				AppendChildWithFirstNonEmptyText(xmlApplicantInformation, "dateofbirth", _webFormConversationDetails.ApplicantInformation.DateOfBirth);
				AppendChildWithFirstNonEmptyText(xmlApplicantInformation, "maritalstatus", _webFormConversationDetails.ApplicantInformation.MaritalStatus);
				AppendChildWithFirstNonEmptyText(xmlApplicantInformation, "homephone", _webFormConversationDetails.ApplicantInformation.HomePhone);
				AppendChildWithFirstNonEmptyText(xmlApplicantInformation, "cellphone", _webFormConversationDetails.ApplicantInformation.CellPhone);
				AppendChildWithFirstNonEmptyText(xmlApplicantInformation, "email", _webFormConversationDetails.ApplicantInformation.Email);

				if (_webFormConversationDetails.ApplicantInformation.ResidenceAddress != null) {
					var xmlResidenceAddress = AppendChildElement(xmlApplicantInformation, "residenceaddress");

					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "address1", _webFormConversationDetails.ApplicantInformation.ResidenceAddress.Address1);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "address2", _webFormConversationDetails.ApplicantInformation.ResidenceAddress.Address2);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "city", _webFormConversationDetails.ApplicantInformation.ResidenceAddress.City);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "state", _webFormConversationDetails.ApplicantInformation.ResidenceAddress.State);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "zip", _webFormConversationDetails.ApplicantInformation.ResidenceAddress.ResidenceZip);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "county", _webFormConversationDetails.ApplicantInformation.ResidenceAddress.County);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "status", _webFormConversationDetails.ApplicantInformation.ResidenceAddress.Status);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "amount", _webFormConversationDetails.ApplicantInformation.ResidenceAddress.Amount);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "years", _webFormConversationDetails.ApplicantInformation.ResidenceAddress.Years);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "month", _webFormConversationDetails.ApplicantInformation.ResidenceAddress.Month);
				}

				if (_webFormConversationDetails.ApplicantInformation.PreviousAddress != null) {
					var xmlPreviousAddress = AppendChildElement(xmlApplicantInformation, "previousaddress");

					AppendChildWithFirstNonEmptyText(xmlPreviousAddress, "address1", _webFormConversationDetails.ApplicantInformation.PreviousAddress.Address1);
					AppendChildWithFirstNonEmptyText(xmlPreviousAddress, "address2", _webFormConversationDetails.ApplicantInformation.PreviousAddress.Address2);
					AppendChildWithFirstNonEmptyText(xmlPreviousAddress, "city", _webFormConversationDetails.ApplicantInformation.PreviousAddress.City);
					AppendChildWithFirstNonEmptyText(xmlPreviousAddress, "state", _webFormConversationDetails.ApplicantInformation.PreviousAddress.State);
					AppendChildWithFirstNonEmptyText(xmlPreviousAddress, "zip", _webFormConversationDetails.ApplicantInformation.PreviousAddress.Zip);
					AppendChildWithFirstNonEmptyText(xmlPreviousAddress, "county", _webFormConversationDetails.ApplicantInformation.PreviousAddress.County);
				}
			}
			#endregion

			#region applicantfinancialinformation
			if (_webFormConversationDetails.ApplicantFinancialInformation != null) {
				var xmlApplicantFinancialInfo = AppendChildElement(xmlData, "applicantfinancialinformation");

				if (_webFormConversationDetails.ApplicantFinancialInformation.CurrentEmployer != null) {
					var xmlCurrentEmployer = AppendChildElement(xmlApplicantFinancialInfo, "currentemployer");

					AppendChildWithFirstNonEmptyText(xmlCurrentEmployer, "name", _webFormConversationDetails.ApplicantFinancialInformation.CurrentEmployer.Name);
					AppendChildWithFirstNonEmptyText(xmlCurrentEmployer, "isselfemployed", _webFormConversationDetails.ApplicantFinancialInformation.CurrentEmployer.IsSelfEmployed);
					AppendChildWithFirstNonEmptyText(xmlCurrentEmployer, "jobtitle", _webFormConversationDetails.ApplicantFinancialInformation.CurrentEmployer.JobTitle);
					AppendChildWithFirstNonEmptyText(xmlCurrentEmployer, "workphone", _webFormConversationDetails.ApplicantFinancialInformation.CurrentEmployer.WorkPhone);
					AppendChildWithFirstNonEmptyText(xmlCurrentEmployer, "years", _webFormConversationDetails.ApplicantFinancialInformation.CurrentEmployer.Years);
					AppendChildWithFirstNonEmptyText(xmlCurrentEmployer, "month", _webFormConversationDetails.ApplicantFinancialInformation.CurrentEmployer.Month);
					AppendChildWithFirstNonEmptyText(xmlCurrentEmployer, "grossincome", _webFormConversationDetails.ApplicantFinancialInformation.CurrentEmployer.GrossIncome);
					AppendChildWithFirstNonEmptyText(xmlCurrentEmployer, "frequency", _webFormConversationDetails.ApplicantFinancialInformation.CurrentEmployer.Frequency);
				}

				if (_webFormConversationDetails.ApplicantFinancialInformation.EmploymentAddress != null) {
					var xmlEmploymentAddress = AppendChildElement(xmlApplicantFinancialInfo, "employmentaddress");

					AppendChildWithFirstNonEmptyText(xmlEmploymentAddress, "address1", _webFormConversationDetails.ApplicantFinancialInformation.EmploymentAddress.Address1);
					AppendChildWithFirstNonEmptyText(xmlEmploymentAddress, "address2", _webFormConversationDetails.ApplicantFinancialInformation.EmploymentAddress.Address2);
					AppendChildWithFirstNonEmptyText(xmlEmploymentAddress, "city", _webFormConversationDetails.ApplicantFinancialInformation.EmploymentAddress.City);
					AppendChildWithFirstNonEmptyText(xmlEmploymentAddress, "state", _webFormConversationDetails.ApplicantFinancialInformation.EmploymentAddress.State);
					AppendChildWithFirstNonEmptyText(xmlEmploymentAddress, "zip", _webFormConversationDetails.ApplicantFinancialInformation.EmploymentAddress.Zip);
				}

				if (_webFormConversationDetails.ApplicantFinancialInformation.PreviousEmployer != null) {
					var xmlPreviousEmployer = AppendChildElement(xmlApplicantFinancialInfo, "previousemployer");

					AppendChildWithFirstNonEmptyText(xmlPreviousEmployer, "name", _webFormConversationDetails.ApplicantFinancialInformation.PreviousEmployer.Name);
					AppendChildWithFirstNonEmptyText(xmlPreviousEmployer, "isselfemployed", _webFormConversationDetails.ApplicantFinancialInformation.PreviousEmployer.IsSelfEmployed);
					AppendChildWithFirstNonEmptyText(xmlPreviousEmployer, "jobtitle", _webFormConversationDetails.ApplicantFinancialInformation.PreviousEmployer.JobTitle);
					AppendChildWithFirstNonEmptyText(xmlPreviousEmployer, "workphone", _webFormConversationDetails.ApplicantFinancialInformation.PreviousEmployer.WorkPhone);
					AppendChildWithFirstNonEmptyText(xmlPreviousEmployer, "years", _webFormConversationDetails.ApplicantFinancialInformation.PreviousEmployer.Years);
					AppendChildWithFirstNonEmptyText(xmlPreviousEmployer, "month", _webFormConversationDetails.ApplicantFinancialInformation.PreviousEmployer.Month);
				}

				if (_webFormConversationDetails.ApplicantFinancialInformation.OtherIncome != null) {
					var xmlOtherIncome = AppendChildElement(xmlApplicantFinancialInfo, "otherincome");

					AppendChildWithFirstNonEmptyText(xmlOtherIncome, "source", _webFormConversationDetails.ApplicantFinancialInformation.OtherIncome.Source);
					AppendChildWithFirstNonEmptyText(xmlOtherIncome, "incomeamount", _webFormConversationDetails.ApplicantFinancialInformation.OtherIncome.Amount);
					AppendChildWithFirstNonEmptyText(xmlOtherIncome, "frequency", _webFormConversationDetails.ApplicantFinancialInformation.OtherIncome.Frequency);
				}

				if (_webFormConversationDetails.ApplicantFinancialInformation.BankInformation != null) {
					var xmlBankInformation = AppendChildElement(xmlApplicantFinancialInfo, "bankinformation");

					AppendChildWithFirstNonEmptyText(xmlBankInformation, "type", _webFormConversationDetails.ApplicantFinancialInformation.BankInformation.Type);
					AppendChildWithFirstNonEmptyText(xmlBankInformation, "name", _webFormConversationDetails.ApplicantFinancialInformation.BankInformation.Name);
					AppendChildWithFirstNonEmptyText(xmlBankInformation, "accnum", _webFormConversationDetails.ApplicantFinancialInformation.BankInformation.AccNum);
					AppendChildWithFirstNonEmptyText(xmlBankInformation, "accbalance", _webFormConversationDetails.ApplicantFinancialInformation.BankInformation.AccBalance);
				}

				if (_webFormConversationDetails.ApplicantFinancialInformation.FinancialHistory != null) {
					var xmlFinancialHistory = AppendChildElement(xmlApplicantFinancialInfo, "financialhistory");

					if (_webFormConversationDetails.ApplicantFinancialInformation.FinancialHistory.BuyerHistory != null) {
						var xmlBuyerHistory = AppendChildElement(xmlFinancialHistory, "buyerhistory");

						AppendChildWithFirstNonEmptyText(xmlBuyerHistory, "numberofunits", _webFormConversationDetails.ApplicantFinancialInformation.FinancialHistory.BuyerHistory.NumberOfUnits);
						AppendChildWithFirstNonEmptyText(xmlBuyerHistory, "numberoftrailers", _webFormConversationDetails.ApplicantFinancialInformation.FinancialHistory.BuyerHistory.NumberOfTrailers);
					}

					if (_webFormConversationDetails.ApplicantFinancialInformation.FinancialHistory.PreviouslyFinancedInformation != null) {
						var xmlPreviouslyFinancedInfo = AppendChildElement(xmlFinancialHistory, "previouslyfinancedinformation");

						AppendChildWithFirstNonEmptyText(xmlPreviouslyFinancedInfo, "financecompanyname", _webFormConversationDetails.ApplicantFinancialInformation.FinancialHistory.PreviouslyFinancedInformation.CompanyName);
						AppendChildWithFirstNonEmptyText(xmlPreviouslyFinancedInfo, "financecompanyphone", _webFormConversationDetails.ApplicantFinancialInformation.FinancialHistory.PreviouslyFinancedInformation.CompanyPhone);
					}

					AppendChildWithFirstNonEmptyText(xmlFinancialHistory, "ishistorybankruptcy", _webFormConversationDetails.ApplicantFinancialInformation.FinancialHistory.FinancialHistoryBankruptcy);
				}
			}
			#endregion

			#region coapplicantinformation
			if (_webFormConversationDetails.CoApplicantInformation != null) {
				var xmlCoApplicantInformation = AppendChildElement(xmlData, "coapplicantinformation");

				AppendChildWithFirstNonEmptyText(xmlCoApplicantInformation, "firstname", _webFormConversationDetails.CoApplicantInformation.FirstName);
				AppendChildWithFirstNonEmptyText(xmlCoApplicantInformation, "initial", _webFormConversationDetails.CoApplicantInformation.Initial);
				AppendChildWithFirstNonEmptyText(xmlCoApplicantInformation, "lastname", _webFormConversationDetails.CoApplicantInformation.LastName);
				AppendChildWithFirstNonEmptyText(xmlCoApplicantInformation, "suffix", _webFormConversationDetails.CoApplicantInformation.Suffix);
				AppendChildWithFirstNonEmptyText(xmlCoApplicantInformation, "socsecnum", _webFormConversationDetails.CoApplicantInformation.SocSecNum);

				if (_webFormConversationDetails.CoApplicantInformation.DriverLicense != null) {
					var xmlDriverLicense = AppendChildElement(xmlCoApplicantInformation, "driverlicense");

					AppendChildWithFirstNonEmptyText(xmlDriverLicense, "licensenumber", _webFormConversationDetails.CoApplicantInformation.DriverLicense.Num);
					AppendChildWithFirstNonEmptyText(xmlDriverLicense, "state", _webFormConversationDetails.CoApplicantInformation.DriverLicense.State);
					AppendChildWithFirstNonEmptyText(xmlDriverLicense, "expirationdate", _webFormConversationDetails.CoApplicantInformation.DriverLicense.Exp);
				}

				AppendChildWithFirstNonEmptyText(xmlCoApplicantInformation, "dateofbirth", _webFormConversationDetails.CoApplicantInformation.DateOfBirth);
				AppendChildWithFirstNonEmptyText(xmlCoApplicantInformation, "maritalstatus", _webFormConversationDetails.CoApplicantInformation.MaritalStatus);
				AppendChildWithFirstNonEmptyText(xmlCoApplicantInformation, "homephone", _webFormConversationDetails.CoApplicantInformation.HomePhone);
				AppendChildWithFirstNonEmptyText(xmlCoApplicantInformation, "cellphone", _webFormConversationDetails.CoApplicantInformation.CellPhone);
				AppendChildWithFirstNonEmptyText(xmlCoApplicantInformation, "email", _webFormConversationDetails.CoApplicantInformation.Email);

				if (_webFormConversationDetails.CoApplicantInformation.ResidenceAddress != null) {
					var xmlResidenceAddress = AppendChildElement(xmlCoApplicantInformation, "residenceaddress");

					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "address1", _webFormConversationDetails.CoApplicantInformation.ResidenceAddress.Address1);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "address2", _webFormConversationDetails.CoApplicantInformation.ResidenceAddress.Address2);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "city", _webFormConversationDetails.CoApplicantInformation.ResidenceAddress.City);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "state", _webFormConversationDetails.CoApplicantInformation.ResidenceAddress.State);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "residencezip", _webFormConversationDetails.CoApplicantInformation.ResidenceAddress.ResidenceZip);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "county", _webFormConversationDetails.CoApplicantInformation.ResidenceAddress.County);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "status", _webFormConversationDetails.CoApplicantInformation.ResidenceAddress.Status);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "amount", _webFormConversationDetails.CoApplicantInformation.ResidenceAddress.Amount);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "years", _webFormConversationDetails.CoApplicantInformation.ResidenceAddress.Years);
					AppendChildWithFirstNonEmptyText(xmlResidenceAddress, "month", _webFormConversationDetails.CoApplicantInformation.ResidenceAddress.Month);
				}

				if (_webFormConversationDetails.CoApplicantInformation.PreviousAddress != null) {
					var xmlPreviousAddress = AppendChildElement(xmlCoApplicantInformation, "previousaddress");

					AppendChildWithFirstNonEmptyText(xmlPreviousAddress, "address1", _webFormConversationDetails.CoApplicantInformation.PreviousAddress.Address1);
					AppendChildWithFirstNonEmptyText(xmlPreviousAddress, "address2", _webFormConversationDetails.CoApplicantInformation.PreviousAddress.Address2);
					AppendChildWithFirstNonEmptyText(xmlPreviousAddress, "city", _webFormConversationDetails.CoApplicantInformation.PreviousAddress.City);
					AppendChildWithFirstNonEmptyText(xmlPreviousAddress, "state", _webFormConversationDetails.CoApplicantInformation.PreviousAddress.State);
					AppendChildWithFirstNonEmptyText(xmlPreviousAddress, "zip", _webFormConversationDetails.CoApplicantInformation.PreviousAddress.Zip);
					AppendChildWithFirstNonEmptyText(xmlPreviousAddress, "county", _webFormConversationDetails.CoApplicantInformation.PreviousAddress.County);
				}
			}
			#endregion

			#region coapplicantfinancialinformation
			if (_webFormConversationDetails.CoApplicantFinancialInformation != null) {
				var xmlCoApplicantFinancialInfo = AppendChildElement(xmlData, "coapplicantfinancialinformation");

				if (_webFormConversationDetails.CoApplicantFinancialInformation.CurrentEmployer != null) {
					var xmlCurrentEmployer = AppendChildElement(xmlCoApplicantFinancialInfo, "currentemployer");

					AppendChildWithFirstNonEmptyText(xmlCurrentEmployer, "name", _webFormConversationDetails.CoApplicantFinancialInformation.CurrentEmployer.Name);
					AppendChildWithFirstNonEmptyText(xmlCurrentEmployer, "selfemployed", _webFormConversationDetails.CoApplicantFinancialInformation.CurrentEmployer.IsSelfEmployed);
					AppendChildWithFirstNonEmptyText(xmlCurrentEmployer, "jobtitle", _webFormConversationDetails.CoApplicantFinancialInformation.CurrentEmployer.JobTitle);
					AppendChildWithFirstNonEmptyText(xmlCurrentEmployer, "workphone", _webFormConversationDetails.CoApplicantFinancialInformation.CurrentEmployer.WorkPhone);
					AppendChildWithFirstNonEmptyText(xmlCurrentEmployer, "years", _webFormConversationDetails.CoApplicantFinancialInformation.CurrentEmployer.Years);
					AppendChildWithFirstNonEmptyText(xmlCurrentEmployer, "month", _webFormConversationDetails.CoApplicantFinancialInformation.CurrentEmployer.Month);
					AppendChildWithFirstNonEmptyText(xmlCurrentEmployer, "grossincome", _webFormConversationDetails.CoApplicantFinancialInformation.CurrentEmployer.GrossIncome);
					AppendChildWithFirstNonEmptyText(xmlCurrentEmployer, "frequency", _webFormConversationDetails.CoApplicantFinancialInformation.CurrentEmployer.Frequency);
				}

				if (_webFormConversationDetails.CoApplicantFinancialInformation.PreviousEmployer != null) {
					var xmlPreviousEmployer = AppendChildElement(xmlCoApplicantFinancialInfo, "previousemployer");

					AppendChildWithFirstNonEmptyText(xmlPreviousEmployer, "name", _webFormConversationDetails.CoApplicantFinancialInformation.PreviousEmployer.Name);
					AppendChildWithFirstNonEmptyText(xmlPreviousEmployer, "selfemployed", _webFormConversationDetails.CoApplicantFinancialInformation.PreviousEmployer.IsSelfEmployed);
					AppendChildWithFirstNonEmptyText(xmlPreviousEmployer, "jobtitle", _webFormConversationDetails.CoApplicantFinancialInformation.PreviousEmployer.JobTitle);
					AppendChildWithFirstNonEmptyText(xmlPreviousEmployer, "workphone", _webFormConversationDetails.CoApplicantFinancialInformation.PreviousEmployer.WorkPhone);
					AppendChildWithFirstNonEmptyText(xmlPreviousEmployer, "years", _webFormConversationDetails.CoApplicantFinancialInformation.PreviousEmployer.Years);
					AppendChildWithFirstNonEmptyText(xmlPreviousEmployer, "month", _webFormConversationDetails.CoApplicantFinancialInformation.PreviousEmployer.Month);
				}

				if (_webFormConversationDetails.CoApplicantFinancialInformation.EmploymentAddress != null) {
					var xmlEmploymentAddress = AppendChildElement(xmlCoApplicantFinancialInfo, "employmentaddress");

					AppendChildWithFirstNonEmptyText(xmlEmploymentAddress, "address1", _webFormConversationDetails.CoApplicantFinancialInformation.EmploymentAddress.Address1);
					AppendChildWithFirstNonEmptyText(xmlEmploymentAddress, "address2", _webFormConversationDetails.CoApplicantFinancialInformation.EmploymentAddress.Address2);
					AppendChildWithFirstNonEmptyText(xmlEmploymentAddress, "city", _webFormConversationDetails.CoApplicantFinancialInformation.EmploymentAddress.City);
					AppendChildWithFirstNonEmptyText(xmlEmploymentAddress, "state", _webFormConversationDetails.CoApplicantFinancialInformation.EmploymentAddress.State);
					AppendChildWithFirstNonEmptyText(xmlEmploymentAddress, "zip", _webFormConversationDetails.CoApplicantFinancialInformation.EmploymentAddress.Zip);
				}

				if (_webFormConversationDetails.CoApplicantFinancialInformation.OtherIncome != null) {
					var xmlOtherIncome = AppendChildElement(xmlCoApplicantFinancialInfo, "otherincome");

					AppendChildWithFirstNonEmptyText(xmlOtherIncome, "source", _webFormConversationDetails.CoApplicantFinancialInformation.OtherIncome.Source);
					AppendChildWithFirstNonEmptyText(xmlOtherIncome, "incomeamount", _webFormConversationDetails.CoApplicantFinancialInformation.OtherIncome.Amount);
					AppendChildWithFirstNonEmptyText(xmlOtherIncome, "frequency", _webFormConversationDetails.CoApplicantFinancialInformation.OtherIncome.Frequency);
				}

				if (_webFormConversationDetails.CoApplicantFinancialInformation.BankInformation != null) {
					var xmlBankInformation = AppendChildElement(xmlCoApplicantFinancialInfo, "bankinformation");

					AppendChildWithFirstNonEmptyText(xmlBankInformation, "type", _webFormConversationDetails.CoApplicantFinancialInformation.BankInformation.Type);
					AppendChildWithFirstNonEmptyText(xmlBankInformation, "name", _webFormConversationDetails.CoApplicantFinancialInformation.BankInformation.Name);
					AppendChildWithFirstNonEmptyText(xmlBankInformation, "accnum", _webFormConversationDetails.CoApplicantFinancialInformation.BankInformation.AccNum);
					AppendChildWithFirstNonEmptyText(xmlBankInformation, "accbalance", _webFormConversationDetails.CoApplicantFinancialInformation.BankInformation.AccBalance);
				}

				if (_webFormConversationDetails.CoApplicantFinancialInformation.FinancialHistory != null) {
					var xmlFinancialHistory = AppendChildElement(xmlCoApplicantFinancialInfo, "financialhistory");

					if (_webFormConversationDetails.CoApplicantFinancialInformation.FinancialHistory.BuyerHistory != null) {
						var xmlBuyerHistory = AppendChildElement(xmlFinancialHistory, "buyerhistory");

						AppendChildWithFirstNonEmptyText(xmlBuyerHistory, "numberofunits", _webFormConversationDetails.CoApplicantFinancialInformation.FinancialHistory.BuyerHistory.NumberOfUnits);
						AppendChildWithFirstNonEmptyText(xmlBuyerHistory, "numberoftrailers", _webFormConversationDetails.CoApplicantFinancialInformation.FinancialHistory.BuyerHistory.NumberOfTrailers);
					}

					if (_webFormConversationDetails.CoApplicantFinancialInformation.FinancialHistory.PreviouslyFinancedInformation != null) {
						var xmlPreviouslyFinancedInfo = AppendChildElement(xmlFinancialHistory, "previouslyfinancedinformation");

						AppendChildWithFirstNonEmptyText(xmlPreviouslyFinancedInfo, "financecompanyname", _webFormConversationDetails.CoApplicantFinancialInformation.FinancialHistory.PreviouslyFinancedInformation.CompanyName);
						AppendChildWithFirstNonEmptyText(xmlPreviouslyFinancedInfo, "financecompanyphone", _webFormConversationDetails.CoApplicantFinancialInformation.FinancialHistory.PreviouslyFinancedInformation.CompanyPhone);
					}

					AppendChildWithFirstNonEmptyText(xmlFinancialHistory, "ishistorybankruptcy", _webFormConversationDetails.CoApplicantFinancialInformation.FinancialHistory.FinancialHistoryBankruptcy);
				}
			}
			#endregion

			#region otherinformation
			if (_webFormConversationDetails.OtherInformation != null) {
				var xmlOtherInformation = AppendChildElement(xmlData, "otherinformation");

				if (_webFormConversationDetails.OtherInformation.References != null) {
					var xmlReferences = AppendChildElement(xmlOtherInformation, "references");

					if (_webFormConversationDetails.OtherInformation.References.Relative != null) {
						var xmlRelative = AppendChildElement(xmlReferences, "relative");

						AppendChildWithFirstNonEmptyText(xmlRelative, "firstname", _webFormConversationDetails.OtherInformation.References.Relative.FirstName);
						AppendChildWithFirstNonEmptyText(xmlRelative, "initialname", _webFormConversationDetails.OtherInformation.References.Relative.InitialName);
						AppendChildWithFirstNonEmptyText(xmlRelative, "lastname", _webFormConversationDetails.OtherInformation.References.Relative.LastName);
						AppendChildWithFirstNonEmptyText(xmlRelative, "suffix", _webFormConversationDetails.OtherInformation.References.Relative.Suffix);
						AppendChildWithFirstNonEmptyText(xmlRelative, "homephone", _webFormConversationDetails.OtherInformation.References.Relative.HomePhone);
						AppendChildWithFirstNonEmptyText(xmlRelative, "address1", _webFormConversationDetails.OtherInformation.References.Relative.Address1);
						AppendChildWithFirstNonEmptyText(xmlRelative, "address2", _webFormConversationDetails.OtherInformation.References.Relative.Address2);
						AppendChildWithFirstNonEmptyText(xmlRelative, "city", _webFormConversationDetails.OtherInformation.References.Relative.City);
						AppendChildWithFirstNonEmptyText(xmlRelative, "state", _webFormConversationDetails.OtherInformation.References.Relative.State);
						AppendChildWithFirstNonEmptyText(xmlRelative, "zip", _webFormConversationDetails.OtherInformation.References.Relative.Zip);
					}

					if (_webFormConversationDetails.OtherInformation.References.Friend != null) {
						var xmlFriend = AppendChildElement(xmlReferences, "friend");

						AppendChildWithFirstNonEmptyText(xmlFriend, "firstname", _webFormConversationDetails.OtherInformation.References.Friend.FirstName);
						AppendChildWithFirstNonEmptyText(xmlFriend, "initial", _webFormConversationDetails.OtherInformation.References.Friend.Initial);
						AppendChildWithFirstNonEmptyText(xmlFriend, "lastname", _webFormConversationDetails.OtherInformation.References.Friend.LastName);
						AppendChildWithFirstNonEmptyText(xmlFriend, "suffix", _webFormConversationDetails.OtherInformation.References.Friend.Suffix);
						AppendChildWithFirstNonEmptyText(xmlFriend, "homephone", _webFormConversationDetails.OtherInformation.References.Friend.HomePhone);
						AppendChildWithFirstNonEmptyText(xmlFriend, "address1", _webFormConversationDetails.OtherInformation.References.Friend.Address1);
						AppendChildWithFirstNonEmptyText(xmlFriend, "address2", _webFormConversationDetails.OtherInformation.References.Friend.Address2);
						AppendChildWithFirstNonEmptyText(xmlFriend, "city", _webFormConversationDetails.OtherInformation.References.Friend.City);
						AppendChildWithFirstNonEmptyText(xmlFriend, "state", _webFormConversationDetails.OtherInformation.References.Friend.State);
						AppendChildWithFirstNonEmptyText(xmlFriend, "zip", _webFormConversationDetails.OtherInformation.References.Friend.Zip);
					}

					if (_webFormConversationDetails.OtherInformation.References.Other != null) {
						var xmlOther = AppendChildElement(xmlReferences, "other");

						AppendChildWithFirstNonEmptyText(xmlOther, "firstname", _webFormConversationDetails.OtherInformation.References.Other.FirstName);
						AppendChildWithFirstNonEmptyText(xmlOther, "initial", _webFormConversationDetails.OtherInformation.References.Other.Initial);
						AppendChildWithFirstNonEmptyText(xmlOther, "lastname", _webFormConversationDetails.OtherInformation.References.Other.LastName);
						AppendChildWithFirstNonEmptyText(xmlOther, "suffix", _webFormConversationDetails.OtherInformation.References.Other.Suffix);
						AppendChildWithFirstNonEmptyText(xmlOther, "phone", _webFormConversationDetails.OtherInformation.References.Other.Phone);
						AppendChildWithFirstNonEmptyText(xmlOther, "address1", _webFormConversationDetails.OtherInformation.References.Other.Address1);
						AppendChildWithFirstNonEmptyText(xmlOther, "address2", _webFormConversationDetails.OtherInformation.References.Other.Address2);
						AppendChildWithFirstNonEmptyText(xmlOther, "city", _webFormConversationDetails.OtherInformation.References.Other.City);
						AppendChildWithFirstNonEmptyText(xmlOther, "state", _webFormConversationDetails.OtherInformation.References.Other.State);
						AppendChildWithFirstNonEmptyText(xmlOther, "zip", _webFormConversationDetails.OtherInformation.References.Other.Zip);
					}
				}

				AppendChildWithFirstNonEmptyText(xmlOtherInformation, "comments", _webFormConversationDetails.OtherInformation.Comments);
				AppendChildWithFirstNonEmptyText(xmlOtherInformation, "typedsignature", _webFormConversationDetails.OtherInformation.TypedSignature);
				AppendChildWithFirstNonEmptyText(xmlOtherInformation, "signaturedate", _webFormConversationDetails.OtherInformation.SignatureDate);
			}
			#endregion

			#region common _notificationModel
			var commonModel = AppendChildElement(xmlData, "leadscommonmodel");

			//dealer contact information
			if (notificationModel.DealerContactInfo != null) {
				var dealerContactInfo = AppendChildElement(commonModel, "contactinfo");
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "firstname", notificationModel.DealerContactInfo.FirstName);
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "lastname", notificationModel.DealerContactInfo.LastName);
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "title", notificationModel.DealerContactInfo.Title);
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "companyname", notificationModel.DealerContactInfo.CompanyName);
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "email", notificationModel.DealerContactInfo.Email);
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "phone", notificationModel.DealerContactInfo.Phone);
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "phoneext", notificationModel.DealerContactInfo.PhoneExt);
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "tollfree", notificationModel.DealerContactInfo.TollFree);
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "mobile", notificationModel.DealerContactInfo.MobilePhone);
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "fax", notificationModel.DealerContactInfo.Fax);
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "country", notificationModel.DealerContactInfo.Country);
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "city", notificationModel.DealerContactInfo.City);
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "state", notificationModel.DealerContactInfo.State);
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "zip", notificationModel.DealerContactInfo.Zip);
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "address1", notificationModel.DealerContactInfo.Address1);
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "address2", notificationModel.DealerContactInfo.Address2);
				AppendChildWithFirstNonEmptyText(dealerContactInfo, "callbright", notificationModel.DealerContactInfo.Callbright);
			}

			//user contact information
			var userContactInformation = AppendChildElement(commonModel, "usercontactinformation");
			AppendChildWithFirstNonEmptyText(userContactInformation, "description", _webFormConversationDetails.UserContactInformation.Description);
			if (_webFormConversationDetails.PreferenceInformation != null) {
				AppendChildWithFirstNonEmptyText(userContactInformation, "preferredday", _webFormConversationDetails.PreferenceInformation.FirstTimePreferenceDate);
				AppendChildWithFirstNonEmptyText(userContactInformation, "preferredtime", _webFormConversationDetails.PreferenceInformation.FirstTimePreferenceTime);
			}

			//shipping details
			if (_webFormConversationDetails.ShippingDetails != null) {
				var shippingDetails = AppendChildElement(commonModel, "shippinginformation");
				AppendChildWithFirstNonEmptyText(shippingDetails, "id", _webFormConversationDetails.ShippingDetails.Id);
				AppendChildWithFirstNonEmptyText(shippingDetails, "totalprice", _webFormConversationDetails.ShippingDetails.TotalPrice);
				AppendChildWithFirstNonEmptyText(shippingDetails, "vehicleid", _webFormConversationDetails.ShippingDetails.VehicleId);
				AppendChildWithFirstNonEmptyText(shippingDetails, "message", _webFormConversationDetails.ShippingDetails.Message);
				AppendChildWithFirstNonEmptyText(shippingDetails, "responsemessage", _webFormConversationDetails.ShippingDetails.ResponseMessage);
				AppendChildWithFirstNonEmptyText(shippingDetails, "iserror", _webFormConversationDetails.ShippingDetails.IsError);
			}

			string leadTypeName = _webFormConversationDetails.RequestInformation.LeadLabel;
			if (string.IsNullOrEmpty(leadTypeName)) {
				leadTypeName = _webFormConversationDetails.RequestInformation.LeadType.GetDescription();
			}

			AppendChildWithFirstNonEmptyText(commonModel, "leadtypename", leadTypeName);
			AppendChildWithFirstNonEmptyText(commonModel, "leadthreadid", _webFormConversationDetails.ConversationDetailsId);
			AppendChildWithFirstNonEmptyText(commonModel, "companyname", notificationModel.DealerContactInfo.CompanyName);

			var leadsSettings = AppendChildElement(commonModel, "leadsettings");

			//lead user email template
			if (communication.NotificationSettings.WebFormNotificationSettings.UserEmailNotificationTemplate != null) {
				var userEmailNotificationTemplate = communication.NotificationSettings.WebFormNotificationSettings.UserEmailNotificationTemplate;
				var leadTemplate = AppendChildElement(leadsSettings, "leadtemplate");

				AppendChildWithFirstNonEmptyText(leadTemplate, "messagebody", userEmailNotificationTemplate.MessageBody);
				AppendChildWithFirstNonEmptyText(leadTemplate, "enabledsignature", userEmailNotificationTemplate.HasToEnableSignature);
				AppendChildWithFirstNonEmptyText(leadTemplate, "hastodisplayvehicledetails", userEmailNotificationTemplate.HasToDisplayVehicleDetails);
				AppendChildWithFirstNonEmptyText(leadTemplate, "usecustomlink", userEmailNotificationTemplate.HasToUseCustomLink);
				AppendChildWithFirstNonEmptyText(leadTemplate, "customlinkhref", userEmailNotificationTemplate.CustomLinkHref);
				AppendChildWithFirstNonEmptyText(leadTemplate, "customlinktext", userEmailNotificationTemplate.CustomLinkText);
			}

			if (_webFormConversationDetails.RequestInformation.CampaignTypeId > 0 && !string.IsNullOrEmpty(_webFormConversationDetails.RequestInformation.CampaignName)) {
				//not all other campaign type
				AppendChildWithFirstNonEmptyText(leadsSettings, "campaignnamecommon", _webFormConversationDetails.RequestInformation.CampaignName);
			}

			//leads common settings
			var leasCommonSettings = AppendChildElement(leadsSettings, "leadcommonsettings");
			AppendChildWithFirstNonEmptyText(leasCommonSettings, "adfsourcetag", accountLeadSettings.NotificationSettings.ADFLeadSource);
			AppendChildWithFirstNonEmptyText(leasCommonSettings, "adfshowproviderid", accountLeadSettings.NotificationSettings.HasToShowProviderIdInADF);
			AppendChildWithFirstNonEmptyText(leasCommonSettings, "dealershipid", accountLeadSettings.NotificationSettings.DealershipId);
			AppendChildWithFirstNonEmptyText(leasCommonSettings, "hastoappendleadtypetoadf", accountLeadSettings.NotificationSettings.HasToAppendLeadTypeToADF);

			//APPS-382 - Customize LeadID sure per account/lead type and campaign
			if (!string.IsNullOrEmpty(communication.WebFormSettings.AdfLeadSourceId)) {
				AppendChildWithFirstNonEmptyText(leasCommonSettings, "adfsourceid", communication.WebFormSettings.AdfLeadSourceId);
			}

			//oem franchised make settings
			if (!string.IsNullOrEmpty(accountLeadSettings.NotificationSettings.ShiftDigitalId) && accountLeadSettings.NotificationSettings.ShiftDigitalType != NotificationEnums.ShiftDigitalTypeEnum.Undefined) {
				var oemFranchisedMakeSettings = AppendChildElement(leadsSettings, "oemfranchisedmakesettings");
				switch (accountLeadSettings.NotificationSettings.ShiftDigitalType) {
					case NotificationEnums.ShiftDigitalTypeEnum.Vw:
						AppendChildWithFirstNonEmptyText(oemFranchisedMakeSettings, "vwfranchisecodevalue", accountLeadSettings.NotificationSettings.ShiftDigitalId);
						break;
					case NotificationEnums.ShiftDigitalTypeEnum.Bmw:
						AppendChildWithFirstNonEmptyText(oemFranchisedMakeSettings, "bmwfranchisecodevalue", accountLeadSettings.NotificationSettings.ShiftDigitalId);
						break;
					case NotificationEnums.ShiftDigitalTypeEnum.Mini:
						AppendChildWithFirstNonEmptyText(oemFranchisedMakeSettings, "minifranchisecodevalue", accountLeadSettings.NotificationSettings.ShiftDigitalId);
						break;
					case NotificationEnums.ShiftDigitalTypeEnum.Lexus:
						AppendChildWithFirstNonEmptyText(oemFranchisedMakeSettings, "lexusfranchisecodevalue", accountLeadSettings.NotificationSettings.ShiftDigitalId);
						break;
					case NotificationEnums.ShiftDigitalTypeEnum.Porsche:
						AppendChildWithFirstNonEmptyText(oemFranchisedMakeSettings, "porschefranchisecodevalue", accountLeadSettings.NotificationSettings.ShiftDigitalId);
						break;
				}
			}
			#endregion

			if (_webFormConversationDetails.RequestInformation.IsAgreeChecked) {
				AppendChildText(AppendChildElement(xmlData, "agreement"), "true");
			}

			return GetXmlDocumentString(xmlDocument);
		}
	}
}
