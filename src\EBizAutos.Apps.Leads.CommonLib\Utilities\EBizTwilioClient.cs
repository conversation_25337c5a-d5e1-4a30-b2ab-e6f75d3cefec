﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Linq;
using EBizAutos.Apps.Leads.CommonLib.Abstract.Clients;
using EBizAutos.Apps.Leads.CommonLib.Configuration;
using EBizAutos.Apps.Leads.CommonLib.Models;
using EBizAutos.Apps.Leads.CommonLib.Models.Twilio;
using EBizAutos.Apps.Leads.CommonLib.Utilities.Helpers;
using EBizAutos.CommonLib.Helpers;
using EBizAutos.CommonLib.PublicConstants;
using EBizAutos.CommonLibCore;
using EBizAutos.CommonLibCore.Helpers;
using Microsoft.Extensions.Caching.Memory;
using Twilio;
using Twilio.Base;
using Twilio.Exceptions;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Rest.Api.V2010.Account.AvailablePhoneNumberCountry;
using Twilio.Rest.Lookups.V1;
using Twilio.Rest.Messaging.V1;
using Twilio.Rest.Pricing.V1.PhoneNumber;
using Twilio.TwiML;
using Twilio.Types;
using static EBizAutos.Apps.Leads.CommonLib.Enums.TwilioEnums;

namespace EBizAutos.Apps.Leads.CommonLib.Utilities {
	public class EBizTwilioClient : ITwilioClient {
		private const int ConstMaxMediaURLsCount = 10;
		private const string ConstCallerNameKeyword = "caller_name";
		private const string _constMessageServiceWithReachedLimitKeyPrefix = "MessageServiceWithReachedLimit_";
		private static readonly TimeSpan _constMessageServiceWithReachedLimitExpirationTimeSpan = TimeSpan.FromHours(2);
		private static readonly TimeSpan _constMessageServiceSeekRetryOnExceptionTimeSpan = TimeSpan.FromMilliseconds(10);
		private const int _constServiceResourcesReadLimit = 100;
		private static readonly Random _random = new Random();

		private readonly List<string> _twilioRequestParamethers = new List<string> { "caller-name" };
		private readonly string _twilioPhonesListingUrlTemplate = "";
		private readonly IMemoryCache _memoryCache = null;

		public EBizTwilioClient(TwilioSettings twilioSettings, IMemoryCache memoryCache) {
			Settings = twilioSettings;
			_twilioPhonesListingUrlTemplate = Settings.ApiUrl + $"/2010-04-01/Accounts/{Settings.AccountSid}/IncomingPhoneNumbers.json?Page={{0}}&PageSize={{1}}";
			_memoryCache = memoryCache;

			TwilioClient.Init(Settings.AccountSid, Settings.AuthToken);
		}

		#region Settings
		public TwilioSettings Settings { get; }
		#endregion

		#region Work with numbers
		public async Task<TwilioPhone> GetPhoneInfoAsync(string number) {
			ResourceSet<IncomingPhoneNumberResource> numbers = await IncomingPhoneNumberResource.ReadAsync(phoneNumber: new PhoneNumber(number));
			IncomingPhoneNumberResource incomingPhoneNumber = numbers.FirstOrDefault();

			if (incomingPhoneNumber == null) {
				return null;
			}

			TwilioPhone phone = TwilioHelpers.ConvertToTwilioPhone(
				incomingPhoneNumber,
				TwilioHelpers.IsTollFreeNumber(incomingPhoneNumber.PhoneNumber.ToString())
			);

			return phone;
		}

		public async Task<TwilioPhone> UpdateTwilioNumberAsync(TwilioPhone phone, bool isDealerNumber) {
			string appSid = isDealerNumber ? Settings.DealerApplicationSid : Settings.CustomerApplicationSid;

			UpdateIncomingPhoneNumberOptions updateOptions = new UpdateIncomingPhoneNumberOptions(phone.PhoneNumberSid) {
				FriendlyName = phone.FriendlyName,
				SmsApplicationSid = appSid,
				VoiceApplicationSid = appSid
			};

			var incomingPhoneNumber = await IncomingPhoneNumberResource.UpdateAsync(updateOptions);

			return TwilioHelpers.ConvertToTwilioPhone(
				incomingPhoneNumber,
				TwilioHelpers.IsTollFreeNumber(incomingPhoneNumber.PhoneNumber.ToString())
			);
		}

		public async Task<bool> DeleteTwilioNumberAsync(string phoneNumberSid) {
			try {
				return await IncomingPhoneNumberResource.DeleteAsync(phoneNumberSid);
			} catch (ApiException apiException) {
				throw new Exception($"An API error has occured during removing Twilio phone number. Status code - {apiException.Status}", apiException);
			} catch (Exception ex) {
				throw new Exception("An unknown error has occured during removing Twilio phone number.", ex);
			}
		}

		public List<TwilioPhone> GetTwilioPhoneNumbers(int pageNumber, int pageSize) {
			string targetUrl = string.Format(_twilioPhonesListingUrlTemplate, pageNumber - 1, pageSize);
			try {
				Page<IncomingPhoneNumberResource> page = IncomingPhoneNumberResource.GetPage(targetUrl, null);

				List<TwilioPhone> result = new List<TwilioPhone>();
				foreach (IncomingPhoneNumberResource incomingPhoneNumber in page.Records) {
					result.Add(TwilioHelpers.ConvertToTwilioPhone(incomingPhoneNumber, TwilioHelpers.IsTollFreeNumber(incomingPhoneNumber.PhoneNumber.ToString())));
				}

				return result;
			} catch (ApiException apiException) {
				throw new Exception($"An API error has occured during retrieving Twilio phone numbers. Status code - {apiException.Status}, Target url - {targetUrl}", apiException);
			} catch (Exception ex) {
				throw new Exception($"An unknown error has occured during retrieving Twilio phone numbers. Target url - {targetUrl}", ex);
			}
		}
		#endregion

		#region SMS & call
		public async Task<TwilioMessage> SendMessageAsync(string numberFrom, string numberTo, string body, List<string> mediaUrls = null) {
			var messageOptions = new CreateMessageOptions(new PhoneNumber(numberTo)) {
				From = new PhoneNumber(numberFrom),
				Body = body,
				StatusCallback = new Uri(Settings.MessageStatusCallbackUrl),
			};

			//for supporting MMS
			if (mediaUrls != null) {
				if (mediaUrls.Count > ConstMaxMediaURLsCount) { // Exceeded the max allowed media URLs count
					return new TwilioMessage() {
						MessageFrom = numberFrom,
						MessageTo = numberTo,
						Body = body,
						MediaUrls = mediaUrls,
						MessageSid = "",
						DateCreated = DateTime.Now,
						MessageStatus = MessageStatusEnum.Failed,
						ErrorCode = TwilioErrorCodeEnum.NumberOfMediaURLsExceedsLimit
					};
				}

				messageOptions.MediaUrl = mediaUrls.Select(x => new Uri(x)).ToList();
			}

			try {
				MessageResource message = await MessageResource.CreateAsync(messageOptions);

				return new TwilioMessage() {
					MessageFrom = numberFrom,
					MessageTo = numberTo,
					Body = body,
					MediaUrls = mediaUrls,
					MessageSid = message.Sid,
					DateCreated = DateTime.Now,
					MessageStatus = GetMessageStatusByStrValue(message.Status.ToString().ToLower()),
					Price = (double?)message.Price
				};
			} catch (ApiException exception) {
				if ((TwilioErrorCodeEnum)exception.Code != TwilioErrorCodeEnum.Undefined) {
					return new TwilioMessage() {
						MessageFrom = numberFrom,
						MessageTo = numberTo,
						Body = body,
						MediaUrls = mediaUrls,
						MessageSid = "",
						DateCreated = DateTime.Now,
						MessageStatus = MessageStatusEnum.Failed,
						ErrorCode = (TwilioErrorCodeEnum)exception.Code
					};
				} else {
					throw;
				}
			}
		}

		public string GetVoiceTwiML(string numberFrom, string numberTo, Communication communication) {
			XElement response = new XElement("Response");

			if (communication.HasToEnableDisclaimer && !string.IsNullOrEmpty(communication.DisclaimerText)) {
				XElement say = new XElement("Say", communication.DisclaimerText);
				say.Add(new XAttribute("voice", "alice"));
				say.Add(new XAttribute("language", "en-US"));

				response.Add(say);
			}


			XElement dial = new XElement("Dial", numberTo);

			if (!string.IsNullOrEmpty(numberFrom)) {
				dial.Add(new XAttribute("callerId", numberFrom));
			}

			if (communication.HasToRecordCall && !string.IsNullOrEmpty(Settings.RecordingStatusCallbackUrl)) {
				dial.Add(new XAttribute("record", "record-from-answer-dual"));
				dial.Add(new XAttribute("recordingStatusCallback", Settings.RecordingStatusCallbackUrl));
				dial.Add(new XAttribute("trim", "trim-silence"));
			}

			response.Add(dial);

			XDeclaration declaration = new XDeclaration("1.0", "utf-8", null);
			XDocument document = new XDocument(declaration, response);

			string resultXml = "";
			using (Utf8StringWriter writer = new Utf8StringWriter()) {
				document.Save(writer);
				resultXml = writer.GetStringBuilder().ToString();
			}

			return resultXml;
		}

		public string GetRejectCallTwiML() {
			XElement response = new XElement("Response");
			XElement reject = new XElement("Reject");

			response.Add(reject);

			XDeclaration declaration = new XDeclaration("1.0", "utf-8", null);
			XDocument document = new XDocument(declaration, response);

			string resultXml = "";
			using (Utf8StringWriter writer = new Utf8StringWriter()) {
				document.Save(writer);
				resultXml = writer.GetStringBuilder().ToString();
			}

			return resultXml;
		}
		#endregion

		#region Get resources info
		public async Task<TwilioMessage> GetMessageInfoAsync(string messageSid) {
			var message = await MessageResource.FetchAsync(messageSid);

			return new TwilioMessage() {
				MessageSid = message.Sid,
				MessageFrom = message.From.ToString(),
				MessageTo = message.To.ToString(),
				Price = message.Price.HasValue ? (double?)Math.Abs(message.Price.Value) : default(double?),
				MessageStatus = GetMessageStatusByStrValue(message.Status.ToString().ToLower()),
				Body = message.Body,
				//MediaUrls = int.Parse(message.NumMedia) > 0 ? await GetMediaUrlsForMMS(message.Sid) : null,
				DateCreated = message.DateCreated.HasValue ? message.DateCreated.Value : default(DateTime)
			};
		}

		public async Task<TwilioCall> GetCallInfoAsync(string callSid) {
			var call = await CallResource.FetchAsync(callSid);

			var childCall = (await CallResource.ReadAsync(parentCallSid: callSid)).FirstOrDefault();

			TwilioCall twilioCall = new TwilioCall() {
				CallSid = call.Sid,
				NumberFrom = call.From,
				NumberTo = call.To,
				CallStatus = GetCallStatusByStrValue(call.Status.ToString().ToLower()),
				Price = call.Price.HasValue ? (double?)Math.Abs(call.Price.Value) : default(double?)
			};

			int duration = 0;
			if (int.TryParse(call.Duration, out duration)) {
				twilioCall.Duration = duration;
			}

			// child call
			if (childCall != null) {
				twilioCall.ChildCall = new TwilioCall() {
					CallSid = childCall.Sid,
					NumberFrom = childCall.From,
					NumberTo = childCall.To,
					CallStatus = GetCallStatusByStrValue(childCall.Status.ToString().ToLower()),
					Price = childCall.Price.HasValue ? (double?)Math.Abs(childCall.Price.Value) : default(double?)
				};

				if (int.TryParse(childCall.Duration, out duration)) {
					twilioCall.ChildCall.Duration = duration;
				}
			}

			return twilioCall;
		}

		public async Task<TwilioRecording> GetCallRecordingInfoAsync(string recordingSid = null, string callSid = null) {
			RecordingResource recording = null;

			if (!string.IsNullOrEmpty(recordingSid)) {
				recording = await RecordingResource.FetchAsync(recordingSid);
			} else if (!string.IsNullOrEmpty(callSid)) {
				recording = (await RecordingResource.ReadAsync(callSid: callSid)).FirstOrDefault();
			} else {
				throw new Exception("Couldn't get recording because recordingSid and callSid parameters are not specified!");
			}

			if (recording == null) {
				return null;
			}

			TwilioRecording twilioRecording = new TwilioRecording() {
				CallSid = recording.CallSid,
				RecordingSid = recording.Sid,
				DateCreated = recording.DateCreated.HasValue ? recording.DateCreated.Value : default(DateTime)
			};

			int duration = 0;
			if (int.TryParse(recording.Duration, out duration)) {
				twilioRecording.Duration = duration;
			}

			decimal price = 0;
			if (decimal.TryParse(recording.Price, out price)) {
				twilioRecording.Price = (double?)Math.Abs(price);
			}

			twilioRecording.TwilioAudioUrl = Settings.ApiUrl + recording.Uri;
			if (twilioRecording.TwilioAudioUrl.ToLower().EndsWith(Constants.ConstJsonExtension)) {
				twilioRecording.TwilioAudioUrl = twilioRecording.TwilioAudioUrl.Substring(0, twilioRecording.TwilioAudioUrl.Length - Constants.ConstJsonExtension.Length);
			}

			twilioRecording.TwilioAudioUrl += Constants.ConstMp3Extension;

			return twilioRecording;
		}

		public async Task<string> GetCallerName(string phoneNumber) {
			PhoneNumberResource lookupResult = await PhoneNumberResource
				.FetchAsync(
					new PhoneNumber(phoneNumber),
					type: _twilioRequestParamethers
				);

			string callerName = "";
			lookupResult?.CallerName?.TryGetValue(ConstCallerNameKeyword, out callerName);
			return callerName;
		}
		#endregion

		#region Remove resources
		public async Task<bool> DeleteCallRecordingAsync(string recordingSid) {
			return await RecordingResource.DeleteAsync(recordingSid);
		}
		#endregion

		#region Pricing
		public async Task<TwilioPricing> GetTwilioPricingAsync(string isoCountryCode) {
			var resource = await ReiterativeTwilioCall(
				async () => await CountryResource.FetchAsync(isoCountryCode),
				Settings.GetPricingCallTimeoutInMs,
				Settings.CountOfTriesToGetPricing
			);

			if (resource.PhoneNumberPrices == null) {
				throw new Exception($"No phone number prices available for country code {isoCountryCode}. Resource data: {resource}");
			}

			TwilioPricing pricing = new TwilioPricing() {
				PhoneNumberPrices = new TwilioPricing.PhoneNumberPricing()
			};

			var numberPrice = resource.PhoneNumberPrices.Find(p => p.NumberType.ToString().ToLower() == TwilioPricing.PhoneNumberPricing.ConstLocalPhoneNumberStr);

			if (numberPrice != null) {
				pricing.PhoneNumberPrices.LocalNumberPrice = numberPrice.CurrentPrice;
			}

			numberPrice = resource.PhoneNumberPrices.Find(p => p.NumberType.ToString().ToLower() == TwilioPricing.PhoneNumberPricing.ConstTollFreePhoneNumberStr);

			if (numberPrice != null) {
				pricing.PhoneNumberPrices.TollFreeNumberPrice = numberPrice.CurrentPrice;
			}

			return pricing;
		}

		public double GetCallerNameLookupPrice() {
			if (Settings.LookupPricing != null) {
				return Settings.LookupPricing.CallerNamePrice;
			}

			return 0.0;
		}
		#endregion

		private async Task<List<LocalResource>> GetLocalResources(TwilioIncomingPhoneNumberSearchOptions numberSearchOptions) {
			ReadLocalOptions readOptions = new ReadLocalOptions(numberSearchOptions.IsoCountryCode) {
				SmsEnabled = true,
				VoiceEnabled = true,
				MmsEnabled = true,
				AreaCode = numberSearchOptions.AreaCode,
				InRegion = numberSearchOptions.StateCode,
				Distance = numberSearchOptions.Distance
			};

			if (numberSearchOptions.Latitude > 0 && numberSearchOptions.Longitude > 0) {
				readOptions.NearLatLong = $"{numberSearchOptions.Latitude},{numberSearchOptions.Longitude}";
			}

			ResourceSet<LocalResource> availableLocalResources = await LocalResource.ReadAsync(readOptions);

			Dictionary<string, LocalResource> result = new Dictionary<string, LocalResource>();
			foreach (LocalResource localResource in availableLocalResources) {
				result[localResource.PhoneNumber.ToString()] = localResource;
			}

			return result.Values.ToList();
		}

		public async Task<IncomingPhoneNumberResource> GetIncomingPhoneNumber(bool isDealerNumber, string friendlyName, TwilioIncomingPhoneNumberSearchOptions numberSearchOptions) {
			IncomingPhoneNumberResource result = null;
			string appSid = isDealerNumber ? Settings.DealerApplicationSid : Settings.CustomerApplicationSid;
			List<LocalResource> resources = await GetLocalResources(numberSearchOptions);

			if (!resources.Any()) {
				return null;
			}

			int triesCount = Math.Min(resources.Count, Settings.AmountOfTriesToBuyPhoneNumber);
			int iteration = 1;
			LocalResource resource = null;
			HashSet<string> alreadyReservedPhoneNumbers = new HashSet<string>();

			try {
				for (; iteration <= triesCount; iteration++) {
					resource = resources
						.Where(x => !alreadyReservedPhoneNumbers.Contains(x.PhoneNumber.ToString()))
						.ElementAt(_random.Next(0, resources.Count - alreadyReservedPhoneNumbers.Count));

					try {
						result = await IncomingPhoneNumberResource.CreateAsync(
							new CreateIncomingPhoneNumberOptions() {
								PhoneNumber = resource.PhoneNumber,
								FriendlyName = friendlyName,
								SmsApplicationSid = appSid,
								VoiceApplicationSid = appSid
							}
						);
						return result;
					} catch (ApiException apiException) {
						if (Settings.ApiErrorCodesForRetry.Contains(apiException.Code)) {
							// For ApiException with code in Settings.ApiErrorCodesForRetry - always retry
							alreadyReservedPhoneNumbers.Add(resource.PhoneNumber.ToString());
						} else {
							// For ApiException with code NOT in Settings.ApiErrorCodesForRetry
							if (apiException.Message.Contains("This phone number is reserved for another account") || apiException.Message.Contains("This number is in use by another account")) {
								alreadyReservedPhoneNumbers.Add(resource.PhoneNumber.ToString());
							} else {
								throw new Exception($"{apiException.Message} Twilio Error Code: {apiException.Code}", apiException);
							}
						}
					} catch (Exception ex) {
						if (ex.Message.Contains("This phone number is reserved for another account") || ex.Message.Contains("This number is in use by another account")) {
							alreadyReservedPhoneNumbers.Add(resource.PhoneNumber.ToString());
						} else {
							throw;
						}
					}
				}
			} catch (Exception ex) {
				string phoneNumbersInfo = "Phone Numbers Info: " + friendlyName
					+ ", isoCountryCode: " + numberSearchOptions.IsoCountryCode
					+ ", isDealerNumber: " + isDealerNumber
					+ ", areaCode: " + (numberSearchOptions.AreaCode?.ToString() ?? "Empty")
					+ ", stateCode: " + (numberSearchOptions.StateCode ?? "Empty")
					+ ", inDistance: " + (numberSearchOptions.Distance?.ToString() ?? "Empty")
					+ ", longitude: " + (numberSearchOptions.Longitude?.ToString() ?? "Empty")
					+ ", latitude: " + (numberSearchOptions.Latitude?.ToString() ?? "Empty")
					+ ", availableLocalNumbersCount: " + resources.Count
					+ ", Current Number: " + (resource?.PhoneNumber ?? "Empty")
					+ ", Try " + iteration + "/" + triesCount
					+ ", Used Local Numbers :";
				phoneNumbersInfo += string.Join(", ", alreadyReservedPhoneNumbers);
				throw new Exception(phoneNumbersInfo, ex);
			}

			return result;
		}

		public async Task AttachPhoneNumberToMessagingServiceAsync(IncomingPhoneNumberResource incomingPhoneNumber) {
			ResourceSet<ServiceResource> serviceResources = await ServiceResource.ReadAsync(limit: _constServiceResourcesReadLimit);

			List<ServiceResource> allServices = serviceResources.ToList();
			if (allServices.Count == 0) {
				throw new Exception("Message Service list is empty");
			}
			
			// Filter services to include only those whose FriendlyName starts with the specified filter text
			string filterText = Settings.MessagingServiceFilter;
			List<ServiceResource> availableServices = allServices
				.Where(service => service.FriendlyName != null && 
				       service.FriendlyName.StartsWith(filterText))
				.ToList();
				
			if (availableServices.Count == 0) {
				throw new Exception($"No messaging services starting with '{filterText}' in their name found.");
			}

			PromiseResult<bool> resultPromise = await RetryHelper.RetryOnExceptionAsync<ApiException, bool>(
				availableServices.Count,
				_constMessageServiceSeekRetryOnExceptionTimeSpan,
				IsMessagingServiceNumberPoolSizeLimitReachedException,
				async (retryContext) => {
					ServiceResource randomAvailableService = GetRandomAvailableService(availableServices);

					if (randomAvailableService == null) {
						throw new ApiException($"Messaging Services limit reached. Can't find any Message Service with unfilled Number Pool for phone number - {incomingPhoneNumber.PhoneNumber}");
					}

					try {
						await Twilio.Rest.Messaging.V1.Service.PhoneNumberResource.CreateAsync(
							phoneNumberSid: incomingPhoneNumber.Sid,
							pathServiceSid: randomAvailableService.Sid
						);
					} catch (ApiException apiException) {
						apiException.Data["PhoneNumberSID"] = incomingPhoneNumber.Sid;
						apiException.Data["PhoneNumber"] = incomingPhoneNumber.PhoneNumber.ToString();
						apiException.Data["ServiceSID"] = randomAvailableService.Sid;
						apiException.Data["TwilioErrorCode"] = apiException.Code;

						if (CheckIfResourceWasNotFoundException(apiException)) {
							apiException.Data["Reason"] = "Twilio provisioned invalid phone number";
						} else if (IsMessagingServiceNumberPoolSizeLimitReachedException(apiException)) {
							MarkMessagingServiceAsLimitReached(randomAvailableService.Sid);
						}

						throw;
					} catch (Exception ex) {
						ex.Data["PhoneNumberSID"] = incomingPhoneNumber.Sid;
						ex.Data["PhoneNumber"] = incomingPhoneNumber.PhoneNumber.ToString();
						ex.Data["ServiceSID"] = randomAvailableService.Sid;

						throw;
					}

					return true;
				},
				CancellationToken.None
			);

			if (resultPromise.Exception != null) {
				if (!IsPhoneNumberAlreadyInTheMessagingServiceException(resultPromise.Exception)) {
					throw resultPromise.Exception;
				}
			}
		}

		private async Task<T> ReiterativeTwilioCall<T>(Func<Task<T>> twilioCall, int timeOutInMs, int countOfTries) {
			T result = default(T);
			for (int i = 0; i < countOfTries; i++) {
				try {
					result = await twilioCall();
					if (result == null) {
						if (i == countOfTries - 1) {
							throw new Exception($"Result of Twilio call is null after {i} try/count iteration(s)");
						}
					} else {
						break;
					}
				} catch {
					if (i == countOfTries - 1) {
						throw;
					}

					await Task.Delay(timeOutInMs);
				}
			}

			return result;
		}

		#region Exception Helpers
		private static bool IsMessagingServiceNumberPoolSizeLimitReachedException(ApiException apiException) {
			return apiException.Code == (int)TwilioErrorCodeEnum.MessagingServiceNumberPoolSizeLimitReached;
		}

		private static bool IsPhoneNumberAlreadyInTheMessagingServiceException(Exception exception) {
			return exception is ApiException apiException && apiException.Code == (int)TwilioErrorCodeEnum.PhoneNumberAlreadyInTheMessagingService;
		}

		private static bool CheckIfResourceWasNotFoundException(ApiException apiException) {
			return apiException.Code == (int)TwilioErrorCodeEnum.TheResourceWasNotFound;
		}
		#endregion

		#region Message Service Helpers
		private ServiceResource GetRandomAvailableService(IList<ServiceResource> availableServices) {
			if (availableServices.Count == 0) {
				return null;
			}

			HashSet<int> exceptionIndexes = new HashSet<int>();

			do {
				int index = RandomHelpers.RandomExceptList(availableServices.Count, exceptionIndexes);
				ServiceResource availableService = availableServices[index];
				if (!IsMessagingServiceLimitReached(availableService.Sid)) {
					return availableService;
				}

				exceptionIndexes.Add(index);
			} while (exceptionIndexes.Count < availableServices.Count);

			return null;
		}

		private void MarkMessagingServiceAsLimitReached(string serviceId) {
			_memoryCache.GetOrCreate($"{_constMessageServiceWithReachedLimitKeyPrefix}{serviceId}", x => {
				x.AbsoluteExpirationRelativeToNow = _constMessageServiceWithReachedLimitExpirationTimeSpan;
				return true;
			});
		}

		private bool IsMessagingServiceLimitReached(string serviceId) {
			return _memoryCache.TryGetValue<bool>($"{_constMessageServiceWithReachedLimitKeyPrefix}{serviceId}", out bool value);
		}
		#endregion
	}
}