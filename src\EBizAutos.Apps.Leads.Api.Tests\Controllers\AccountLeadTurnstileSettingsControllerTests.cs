using System.Collections.Generic;
using EBizAutos.ApplicationCommonLib.Applications.Enums;
using EBizAutos.Apps.Leads.Api.Models.AccountLeadSettings;
using EBizAutos.Apps.Leads.CommonLib.Models.Turnstile;
using Xunit;

namespace EBizAutos.Apps.Leads.Api.Tests.Controllers {
    public class AccountLeadTurnstileSettingsControllerTests {
        
        [Fact]
        public void ViewModelAccountLeadTurnstileSettingsAdmin_ShouldInitializeCorrectly() {
            // Arrange & Act
            var viewModel = new ViewModelAccountLeadTurnstileSettingsAdmin();
            
            // Assert
            Assert.NotNull(viewModel.LeadTypeSettings);
            Assert.Empty(viewModel.LeadTypeSettings);
            Assert.Equal(0, viewModel.AccountId);
            Assert.False(viewModel.IsTurnstileEnabled);
        }

        [Fact]
        public void ViewModelAccountLeadTurnstileSettingsDealer_ShouldInitializeCorrectly() {
            // Arrange & Act
            var viewModel = new ViewModelAccountLeadTurnstileSettingsDealer();
            
            // Assert
            Assert.NotNull(viewModel.LeadTypeSettings);
            Assert.Empty(viewModel.LeadTypeSettings);
            Assert.Equal(0, viewModel.AccountId);
            Assert.False(viewModel.IsTurnstileEnabled);
        }

        [Fact]
        public void ViewModelTurnstileLeadTypeSpecificSetting_ShouldSetPropertiesCorrectly() {
            // Arrange
            var leadType = ApplicationEnums.Leads.LeadTypeEnum.Email;
            var isActive = true;
            
            // Act
            var setting = new ViewModelTurnstileLeadTypeSpecificSetting {
                LeadType = leadType,
                IsActive = isActive
            };
            
            // Assert
            Assert.Equal(leadType, setting.LeadType);
            Assert.Equal(isActive, setting.IsActive);
        }

        [Fact]
        public void ViewModelAccountLeadTurnstileSettingsAdmin_ShouldAllowSettingAllProperties() {
            // Arrange
            var accountId = 123;
            var siteKey = "test-site-key";
            var secretKey = "test-secret-key";
            var failureMessage = "Test failure message";
            var failureAction = TurnstileFailureActionEnum.AcceptAndGoToSpam;
            var leadTypeSettings = new List<ViewModelTurnstileLeadTypeSpecificSetting> {
                new ViewModelTurnstileLeadTypeSpecificSetting {
                    LeadType = ApplicationEnums.Leads.LeadTypeEnum.Email,
                    IsActive = true
                }
            };
            
            // Act
            var viewModel = new ViewModelAccountLeadTurnstileSettingsAdmin {
                AccountId = accountId,
                IsTurnstileEnabled = true,
                SiteKey = siteKey,
                SecretKey = secretKey,
                FailureMessage = failureMessage,
                FailureAction = failureAction,
                LeadTypeSettings = leadTypeSettings
            };
            
            // Assert
            Assert.Equal(accountId, viewModel.AccountId);
            Assert.True(viewModel.IsTurnstileEnabled);
            Assert.Equal(siteKey, viewModel.SiteKey);
            Assert.Equal(secretKey, viewModel.SecretKey);
            Assert.Equal(failureMessage, viewModel.FailureMessage);
            Assert.Equal(failureAction, viewModel.FailureAction);
            Assert.Single(viewModel.LeadTypeSettings);
            Assert.Equal(ApplicationEnums.Leads.LeadTypeEnum.Email, viewModel.LeadTypeSettings[0].LeadType);
            Assert.True(viewModel.LeadTypeSettings[0].IsActive);
        }

        [Fact]
        public void ViewModelAccountLeadTurnstileSettingsDealer_ShouldNotHaveSiteKeyAndSecretKey() {
            // Arrange
            var viewModel = new ViewModelAccountLeadTurnstileSettingsDealer();
            
            // Act & Assert
            // Verify that dealer model doesn't have SiteKey and SecretKey properties
            var type = typeof(ViewModelAccountLeadTurnstileSettingsDealer);
            Assert.Null(type.GetProperty("SiteKey"));
            Assert.Null(type.GetProperty("SecretKey"));
            
            // But should have other properties
            Assert.NotNull(type.GetProperty("AccountId"));
            Assert.NotNull(type.GetProperty("IsTurnstileEnabled"));
            Assert.NotNull(type.GetProperty("FailureMessage"));
            Assert.NotNull(type.GetProperty("FailureAction"));
            Assert.NotNull(type.GetProperty("LeadTypeSettings"));
        }
    }
}
