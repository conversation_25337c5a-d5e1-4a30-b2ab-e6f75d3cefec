{
	"AppSettings": {
		"ApplicationName": "Apps EBay Processor (dev)",
		"DataAccessEncryptKey": "fpABRddPOOg0hbm1PUHIjw==",
		"DataAccessEncryptIV": "AAAAAAAAAAAAAAAAAAAAAA==",
		"EBayAdFormatLeadsEmailAddress": "<EMAIL>",
		"EBizAutosMainConsoleUrl": "https://cp.ebizautos.com/",
		"IsDev": true,
		"CleanUpClosedAccountAfterDays": 30
	},
	"DbSettings": {
		"EBayMongoDbConnectionString": "mongodb+srv://ebizdev:<EMAIL>/ebay?retryWrites=true&w=majority",
		"EBayMsSqlConnectionString": "server=dev-sql.internal.aws.ebizautos.com; database=EBizAutos; User ID=sa; Pwd=********; Max Pool Size=300; Connection Timeout=30; Application Name=Ebay (live);",
		"AppsAccountsMongoDbConnectionString": "mongodb+srv://ebizdev:<EMAIL>/accounts?retryWrites=true&w=majority",
		"SiteBoxManagementMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/siteboxes?retryWrites=true&w=majority"
	},
	"EBayApiClientSettings": {
		"Version": "981",
		"Timeout": 60000,
		"SoapApiServerUrl": "https://api.sandbox.ebay.com/wsapi",
		"EpsServerUrl": "https://msa-e1.sandbox.ebay.com/ws/eBayISAPI.dll?EpsBasicApp",
		"SignInUrl": "https://signin.sandbox.ebay.com/ws/eBayISAPI.dll?SignIn",
		"DevId": "d8f218e5-6e31-4653-a374-491b6197c65c",
		"AppId": "AndrwMan-appsdevw-SBX-5a5965275-d651ed03",
		"CertId": "SBX-a59652756468-13e9-4bee-aa00-d6eb",
		"EnableMetrics": false,
		"RetryAttempts": 5,
		"MinRetryDelayInMs": 1000,
		"MaxRetryDelayInMs": 5000,
		"OAuth2RedirectionUrl": "https://apps.sandbox.ebizautos.com/api/ebay/user/auth/update",
		"OAuth2ClientScopes": [
			"https://api.ebay.com/oauth/api_scope",
			"https://api.ebay.com/oauth/api_scope/sell.inventory",
			"https://api.ebay.com/oauth/api_scope/sell.account",
			"https://api.ebay.com/oauth/api_scope/sell.item",
			"https://api.ebay.com/oauth/api_scope/commerce.notification.subscription",
			"https://api.ebay.com/oauth/api_scope/sell.marketing.readonly"
		],
		"MongoDbCacheConnectionString": "mongodb+srv://ebizdev:<EMAIL>/ebay?retryWrites=true&w=majority"
	},
	"EmailServiceSettings": {
		"MailServerHost": "127.0.0.1",
		"MailServerPort": 1025,
		"DefaultFromEmail": "<EMAIL>",
		"EmailTypesSettings": {
			"EBayTokenExpired": {
				"From": "<EMAIL>",
				"To": "<EMAIL>"
			},
			"CatchAll": {
				"From": "<EMAIL>",
				"To": "<EMAIL>"
			}
		}
	},
	"ServiceBusSettings": {
		"Host": "b-14921ad0-337a-4458-84ee-c042b1327094-1.mq.us-east-1.amazonaws.com",
		"Port": 61617,
		"Username": "ebizeventbus",
		"Password": "z$32s0d-nd8G62!js83aPuhpUh",
		"FailoverHosts": [
			"b-14921ad0-337a-4458-84ee-c042b1327094-1.mq.us-east-1.amazonaws.com"
		],
		"HasToUseSsl": true,
		"IsLoggingOn": true,
		"LogsMongoDbRepositoryConnectionString": "mongodb+srv://ebizdev:<EMAIL>/appsservicebuslogs?retryWrites=true&w=majority",
		"PublishSettings": {
			"RetryAttempts": 3,
			"MinRetryDelayInMs": 5000,
			"MaxRetryDelayInMs": 10000
		},
		"ReceiveEndpointSettings": {
			//user contact update configuration
			"AuctionOfferProcessedEndpointEnabled": true,
			"AuctionOfferProcessedEndpointConsumersCount": 1,
			"AuctionOfferProcessedEndpointRetries": 1,
			"AuctionOfferProcessedEndpointRetriesIntervalInSec": 3,
			//vehicle updated, deleted and auction synced events processor
			"VehicleEventsEndpointEnabled": true,
			"VehicleEventsEndpointConsumersCount": 1,
			"VehicleEventsEndpointRetries": 1,
			"VehicleEventsEndpointRetriesIntervalInSec": 3,
			"VehicleEventsEndpointPrefetchCount": 10,
			"LegacyContactEventsEndpointSettings": {
				//MS SQL contact events processor
				"IsEndpointEnabled": true,
				"ConsumersCount": 1,
				"RetriesCount": 3,
				"RetriesIntervalInSec": 3,
				"IsRedeliveryEnabled": true,
				"RedeliveryCount": 3,
				"RedeliveryTimeoutInMin": 30
			},
			"LegacyAccountEventsEndpointSettings": {
				//MS SQL account events processor
				"IsEndpointEnabled": true,
				"ConsumersCount": 1,
				"RetriesCount": 3,
				"RetriesIntervalInSec": 3,
				"IsRedeliveryEnabled": true,
				"RedeliveryCount": 3,
				"RedeliveryTimeoutInMin": 30
			},
			"LegacyAuctionEventsEndpointSettings": {
				//MS SQL auction events processor
				"IsEndpointEnabled": true,
				"ConsumersCount": 1,
				"RetriesCount": 3,
				"RetriesIntervalInSec": 3,
				"IsRedeliveryEnabled": true,
				"RedeliveryCount": 3,
				"RedeliveryTimeoutInMin": 30
			},
			"AuctionEndedReceiveEndpointSettings": {
				//MS SQL update auction history
				"IsEndpointEnabled": true,
				"ConsumersCount": 1,
				"RetriesCount": 1,
				"RetriesIntervalInSec": 3,
				"IsRedeliveryEnabled": true,
				"RedeliveryCount": 3,
				"RedeliveryTimeoutInMin": 30
			},
			"AuctionProcessingReceiveEndpointSettings": {
				//Auction Processing Receive Endpoint (Update End of Auction)
				"IsEndpointEnabled": true,
				"ConsumersCount": 1,
				"RetriesCount": 1,
				"RetriesIntervalInSec": 3,
				"IsRedeliveryEnabled": true,
				"RedeliveryCount": 3,
				"RedeliveryTimeoutInMin": 30
			},
			"EBayNotificationCommandsEndpointSettings": {
				//EBay Web Hook Notifications
				"IsEndpointEnabled": true,
				"ConsumersCount": 1,
				"RetriesCount": 1,
				"RetriesIntervalInSec": 3,
				"IsRedeliveryEnabled": false,
				"RedeliveryCount": 1,
				"RedeliveryTimeoutInMin": 10
			},
			"AccountEventsReceiveEnpointSettings": {
				"IsAccountEventsEndpointEnabled": true,
				"AccountEventsEndpointConsumersCount": 1,
				"AccountEventsEndpointRetries": 1,
				"AccountEventsEndpointRetriesIntervalInSec": 300,
				"IsAccountEventsRedeliveryEnabled": true,
				"AccountEventsRedeliveryCount": 2,
				"AccountEventsRedeliveryTimeoutInMinutes": 2
			},
			"ContactsReceiveEnpointSettings": {
				"IsContactMessagesEndpointEnabled": true,
				"ContactMessagesEndpointConsumersCount": 1,
				"ContactMessagesEndpointRetries": 1,
				"ContactMessagesEndpointRetriesIntervalInSec": 3,
				"IsContactMessagesEndpointRedeliveryEnabled": true,
				"ContactMessagesEndpointRedeliveryCount": 3,
				"ContactMessagesEndpointRedeliveryTimeoutInMinutes": 30
			}
		}
	},
	"CronJobsSettings": {
		"EBayCategoriesSynchronizationJobSettings": {
			"IsEnabled": true,
			"IsLoggingEnabled": true,
			"CronExpressions": [
				"* * * * * *"
			]
			// daily at 2 AM
		},
		"EBayAttributesSynchronizationJobSettings": {
			"IsEnabled": false,
			"IsLoggingEnabled": true,
			"CronExpressions": [
				"0 0 1/3 * * *"
			]
			// every 3rd hour from 1AM
		},
		"VehiclesToDeleteScannerJobSettings": {
			"IsEnabled": false,
			"IsLoggingEnabled": true,
			"CronExpressions": [
				"0 0 1/3 * * *"
			]
			// every 3rd hour from 1AM
		},
		"EBayLocalBulkReviseProcessorJobSettings": {
			"IsEnabled": false,
			"IsLoggingEnabled": true,
			"CronExpressions": [
				"0 0 1/12 * * *"
			] // every 12th hour from 1AM
		},
		"LaunchLocalScheduledItemsJobSettings": {
			"IsEnabled": false,
			"IsLoggingEnabled": true,
			"CronExpressions": [
				"0 4/3 0 * * *",
				"0 1/3 1-17 * * *",
				"0 1-55/3 18 * * *",
				"0 10/3 20 * * *",
				"0 1/3 21-22 * * *",
				"0 1-58/3 23 * * *"
			] // every 3 minutes between 12:04 AM and 6:55 PM and every 3 minutes between 8:10 PM and 11:58 PM
		},
		"LaunchNationalScheduledItemsJobSettings": {
			"IsEnabled": false,
			"IsLoggingEnabled": true,
			"CronExpressions": [
				"0 1/5 * * * *"
			] // every 5 minutes between 12:01 AM and 11:59 PM
		},
		"LaunchManualScheduledItemsJobSettings": {
			"IsEnabled": false,
			"IsLoggingEnabled": true,
			"CronExpressions": [
				"0/3 * * * * *"
			] // every 3 second
		},
		"RefreshMoreThanThreeDaysRemainingAuctionsWatchCountJobSettings": {
			"IsEnabled": false,
			"IsLoggingEnabled": true,
			"CronExpressions": [
				"0 12 3 * * *",
				"0 12 15 * * *"
			] // every 12 hours between 3:12 AM and 11:55 PM
		},
		"RefreshOneDayRemainingAuctionsWatchCountJobSettings": {
			"IsEnabled": false,
			"IsLoggingEnabled": true,
			"CronExpressions": [
				"0 5 5 * * *",
				"0 5 9 * * *",
				"0 5 13 * * *",
				"0 5 17 * * *",
				"0 5 21 * * *"
			] // every 4 hours between 5:05 AM and 11:30 PM
		},
		"RefreshOneHourRemainingAuctionsWatchCountJobSettings": {
			"IsEnabled": false,
			"IsLoggingEnabled": true,
			"CronExpressions": [
				"0 0/20 1 * * *",
				"0 0/20 2 * * *",
				"0 0/20 3 * * *",
				"0 0/20 4 * * *",
				"0 0/20 5 * * *",
				"0 0/20 6 * * *",
				"0 0/20 7 * * *",
				"0 0/20 8 * * *",
				"0 0/20 9 * * *",
				"0 0/20 10 * * *",
				"0 0/20 11 * * *",
				"0 0/20 12 * * *",
				"0 0/20 13 * * *",
				"0 0/20 14 * * *",
				"0 0/20 15 * * *",
				"0 0/20 16 * * *",
				"0 0/20 17 * * *",
				"0 0/20 18 * * *",
				"0 0/20 19 * * *",
				"0 0/20 20 * * *",
				"0 0/20 21 * * *",
				"0 0/20 22 * * *",
				"0 0-45/20 23 * * *"
			] // every 20 minutes between 1:00 AM and 11:45 PM
		},
		"RefreshOneToThreeDaysRemainingAuctionsWatchCountJobSettings": {
			"IsEnabled": false,
			"IsLoggingEnabled": true,
			"CronExpressions": [
				"0 20 1 * * *",
				"0 20 10 * * *",
				"0 20 19 * * *"
			] // every 9 hours between 1:20 AM and 11:16 PM
		},
		"RefreshThreeToTwentyOneDaysRemainingAuctionsWatchCountJobSettings": {
			"IsEnabled": false,
			"IsLoggingEnabled": true,
			"CronExpressions": [
				"0 13 2 * * *",
				"0 13 14 * * *"
			] // every 12 hours between 2:13 AM and 11:26 PM
		},
		"ScheduleItemsJobSettings": {
			"IsEnabled": true,
			"IsLoggingEnabled": true,
			"CronExpressions": [
				"0 0 0/1 * * *"
			] // every hour
		},
		"CleanUpClosedAccountsJobSettings": {
			"IsEnabled": false,
			"IsLoggingEnabled": true,
			"CronExpressions": [
				"0 0 0/6 * * *"
			] // every 6 hours
		}
	},
	"EBayLocalBulkReviseSettings": {
		"ButchSize": 50
	},
	"ActorsSystemSettings": {
		"IsAuctionProcessorTurnedOn": false,
		"IsRestoreFailedEventsTurnedOn": true,
		"StandardRangeProcessors": [
			{
				//3To10Days settings
				"RangeName": "3To10DaysProcessor",
				"SleepingTimeInSeconds": 7200,
				"OnReadingListErrorSleepingTimeInSeconds": 60,
				"TimeRangeFromInSeconds": 259200,
				//3 days
				"TimeRangeToInSeconds": 864000
				// 10 days
			},
			{
				//1To3Days settings
				"RangeName": "1To3DaysProcessor",
				"SleepingTimeInSeconds": 3600,
				"OnReadingListErrorSleepingTimeInSeconds": 60,
				"TimeRangeFromInSeconds": 86400,
				//1 day
				"TimeRangeToInSeconds": 259200
				// 3 days
			},
			{
				//1To24Hours settings
				"RangeName": "1To24HoursProcessor",
				"SleepingTimeInSeconds": 1200,
				"OnReadingListErrorSleepingTimeInSeconds": 60,
				"TimeRangeFromInSeconds": 3600,
				//1 hour
				"TimeRangeToInSeconds": 86400
				// 24 hours
			},
			{
				//1MinuteTo1Hour settings
				"RangeName": "1MinuteTo1HourProcessor",
				"SleepingTimeInSeconds": 600,
				"OnReadingListErrorSleepingTimeInSeconds": 10,
				"TimeRangeFromInSeconds": 60,
				//1 minute
				"TimeRangeToInSeconds": 3600
				// 1 hour
			},
			{
				//5MinutesPast settings
				"RangeName": "5MinutesPastProcessor",
				"SleepingTimeInSeconds": 60,
				"OnReadingListErrorSleepingTimeInSeconds": 10,
				"TimeRangeFromInSeconds": -290,
				// -4 minutes 50 seconds
				"TimeRangeToInSeconds": 0
				// Now
			}
		],
		"LessThanMinuteRangeProcessor": {
			//UpTo1Minute
			"MaxSleepingTimeInSeconds": 30,
			"OnReadingListErrorSleepingTimeInSeconds": 5,
			"AuctionsPerProcessorActor": 3
		},
		"RestoreFailedEventsProcessorSettings": {
			"StartupDelayInMs": 1000,
			"SuccessSpinDelayInMs": 2000,
			"FailedSpinDelayInMs": 10000,
			"AssumedTaskResponseTimeoutInMs": 10000,
			"EventsBatchSize": 20
		}
	},
	"CommandAndEventHandlingSettings": {
		"RetryCount": 3,
		"MinRetryDelayInMs": 1000,
		"MaxRetryDelayInMs": 1000,
		"ExceptionNamesToRetry": [
			"DBConcurrencyException"
		]
	},
	"ExceptionSettings": {
		"ErrorWebServiceLocation": "http://errors.aws.ebizautos.com/queuedservice.asmx",
		"ApplicationCategoryId": 9,
		"MailServer": "l2ms04.ebizautos.colo",
		"ErrorEmailFrom": "<EMAIL>",
		"ErrorEmailTo": [
			"<EMAIL>"
		],
		"EmailCc": [],
		"EmailCacheTimeInMinutes": 1,
		"HasToUseServiceBus": false
	}
}
